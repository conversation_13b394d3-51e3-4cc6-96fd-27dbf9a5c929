#!/usr/bin/env python3
"""
测试直接模式（禁用子Agent）
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlite_agent.agent import sqlite_agent, ENABLE_SUB_AGENTS
from sqlite_agent.llm_monitor import enable_monitoring
from google.adk.runners import InMemoryRunner
from google.genai import types


async def test_direct_mode():
    """测试直接模式"""
    
    print("=" * 60)
    print("测试直接模式（禁用子Agent）")
    print("=" * 60)
    
    # 检查配置
    print(f"ENABLE_SUB_AGENTS: {ENABLE_SUB_AGENTS}")
    
    if ENABLE_SUB_AGENTS:
        print("❌ 子Agent模式仍然启用")
        return
    else:
        print("✅ 子Agent模式已禁用")
    
    # 检查工具
    tools = sqlite_agent.tools
    print(f"可用工具数量: {len(tools)}")
    
    sub_agent_count = 0
    for i, tool in enumerate(tools):
        if hasattr(tool, 'name'):
            print(f"  {i+1}. {tool.name}")
        elif hasattr(tool, 'agent'):
            print(f"  {i+1}. AgentTool({tool.agent.name})")
            sub_agent_count += 1
        else:
            print(f"  {i+1}. {type(tool).__name__}")
    
    if sub_agent_count > 0:
        print(f"❌ 仍然存在 {sub_agent_count} 个子Agent工具")
        return
    else:
        print("✅ 已移除所有子Agent工具")
    
    # 启用监控
    enable_monitoring(detailed_logging=True, save_to_file=False)

    # 创建Runner
    runner = InMemoryRunner(
        agent=sqlite_agent,
        app_name="sqlite_test"
    )

    # 创建会话
    session = await runner.session_service.create_session(
        app_name="sqlite_test",
        user_id="test_user"
    )

    # 执行测试查询
    print("\n" + "=" * 60)
    print("执行测试查询")
    print("=" * 60)

    try:
        print("查询: 显示数据库中有哪些表")

        # 创建用户消息
        content = types.Content(
            role="user",
            parts=[types.Part.from_text(text="显示数据库中有哪些表")]
        )

        # 收集所有事件
        events = []
        final_response = ""

        async for event in runner.run_async(
            user_id="test_user",
            session_id=session.id,
            new_message=content
        ):
            events.append(event)

            # 收集最终响应
            if event.content and event.content.parts:
                for part in event.content.parts:
                    if hasattr(part, 'text') and part.text and event.author != "user":
                        final_response += part.text

        print(f"✅ 查询成功")
        print(f"事件数量: {len(events)}")
        print(f"响应长度: {len(final_response)} 字符")
        print(f"响应预览: {final_response[:200]}...")

    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_direct_mode())
