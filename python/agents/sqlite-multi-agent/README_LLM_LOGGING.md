# SQLite Agent LLM交互原始报文保存功能

## 🎯 功能概述

SQLite Agent现在支持将与LLM的所有交互原始报文保存到文件中，包括：
- 完整的API请求和响应数据
- Token使用统计
- 响应时间分析
- 交互类型分类
- 会话总结报告

## 📁 文件结构

启用文件保存后，会在指定目录（默认`llm_logs`）中生成以下文件：

```
llm_logs/
├── llm_interactions_20250717_184638.jsonl    # 详细交互记录
└── llm_interactions_20250717_184638_summary.json  # 会话总结
```

### 📄 交互记录文件 (*.jsonl)

每行一个JSON对象，包含单次LLM交互的完整信息：

```json
{
  "interaction_id": 1,
  "timestamp": "18:46:38.112",
  "interaction_type": "function_call",
  "input_content": "用户查询: 显示数据库中有哪些表",
  "output_content": "",
  "function_name": "list_tables",
  "function_args": {},
  "input_tokens": 12,
  "output_tokens": 0,
  "total_tokens": 12,
  "duration_ms": 1250.5,
  "raw_request": {
    "model": "deepseek/deepseek-chat",
    "messages": [...],
    "tools": [...],
    "temperature": 0.1,
    "top_p": 0.8,
    "max_tokens": 2048
  },
  "raw_response": {
    "id": "chatcmpl-test-1",
    "object": "chat.completion",
    "model": "deepseek-chat",
    "choices": [...],
    "usage": {
      "prompt_tokens": 770,
      "completion_tokens": 13,
      "total_tokens": 783
    }
  }
}
```

### 📊 会话总结文件 (*_summary.json)

包含整个会话的统计信息：

```json
{
  "session_summary": {
    "timestamp": "2025-07-17T18:46:38.218052",
    "total_interactions": 2,
    "total_input_tokens": 24,
    "total_output_tokens": 29,
    "total_tokens": 53,
    "total_duration_ms": 3350.8,
    "average_duration_per_interaction": 1675.4,
    "average_input_tokens": 12.0,
    "average_output_tokens": 14.5,
    "input_token_ratio": 45.28,
    "output_token_ratio": 54.72,
    "interaction_types": {
      "function_call": 1,
      "function_response": 1
    }
  }
}
```

## 🔧 配置和使用

### 启用文件保存

文件保存功能默认已启用。在`sqlite_agent/agent.py`中：

```python
# 启用LLM监控，包括文件保存
enable_monitoring(
    detailed_logging=True, 
    max_content_length=2000, 
    show_raw_api=True,
    save_to_file=True,        # 启用文件保存
    output_dir="llm_logs"     # 输出目录
)
```

### 配置参数

- `save_to_file`: 是否保存到文件 (默认: True)
- `output_dir`: 输出目录 (默认: "llm_logs")
- `detailed_logging`: 是否显示详细日志 (默认: True)
- `show_raw_api`: 是否显示原始API数据 (默认: True)
- `max_content_length`: 内容显示长度限制 (默认: 2000)

### 手动保存会话总结

```python
from sqlite_agent.llm_monitor import save_session_summary

# 保存当前会话总结
save_session_summary()
```

## 📖 日志查看器

使用内置的日志查看器分析保存的数据：

### 基本用法

```bash
# 查看日志概览
python3 log_viewer.py llm_logs

# 查看详细交互记录
python3 log_viewer.py llm_logs --detail

# 查看详细分析
python3 log_viewer.py llm_logs --analysis

# 查看特定交互的原始API数据
python3 log_viewer.py llm_logs --interaction 1

# 查看详细记录和原始API数据
python3 log_viewer.py llm_logs --detail --raw
```

### 命令行参数

- `log_dir`: 日志目录路径 (必需)
- `--detail, -d`: 显示详细交互信息
- `--raw, -r`: 显示原始API请求/响应
- `--analysis, -a`: 显示详细分析
- `--interaction, -i`: 显示指定交互的详细信息

### 输出示例

```
📊 交互记录概览
================================================================================
📋 总交互次数: 2
🎯 总Token消耗: 53
⏱️ 总耗时: 3350.8ms
📈 平均每次交互: 1675.4ms
📋 交互类型分布:
   • function_call: 1次
   • function_response: 1次

📈 Token使用分析
================================================================================
📊 Token统计:
   输入Token: 24
   输出Token: 29
   总计Token: 53
   输入占比: 45.3%
   输出占比: 54.7%

📋 按交互类型分析:
   function_call:
     总Token: 12 (平均: 12/次)
     输入: 12, 输出: 0
   function_response:
     总Token: 41 (平均: 41/次)
     输入: 12, 输出: 29

⚡ 性能分析
================================================================================
⏱️ 响应时间统计:
   总耗时: 3350.8ms
   平均耗时: 1675.4ms
   最快响应: 1250.5ms
   最慢响应: 2100.3ms
```

## 🔍 数据分析用途

保存的原始报文可用于：

1. **性能分析**: 分析响应时间、Token使用效率
2. **成本计算**: 精确计算API调用成本
3. **调试优化**: 分析LLM的决策过程和工具选择
4. **质量评估**: 评估回答质量和准确性
5. **模型对比**: 对比不同模型的表现
6. **审计追踪**: 完整的交互历史记录

## 🛠️ 高级用法

### 自定义分析脚本

```python
import json

# 读取JSONL文件
def load_interactions(file_path):
    interactions = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                interactions.append(json.loads(line))
    return interactions

# 分析Token使用模式
def analyze_token_patterns(interactions):
    for interaction in interactions:
        print(f"交互{interaction['interaction_id']}: "
              f"{interaction['total_tokens']} tokens, "
              f"{interaction['duration_ms']:.1f}ms")

# 使用示例
interactions = load_interactions('llm_logs/llm_interactions_20250717_184638.jsonl')
analyze_token_patterns(interactions)
```

### 数据导出

```python
import pandas as pd

# 转换为DataFrame进行分析
def to_dataframe(interactions):
    data = []
    for interaction in interactions:
        data.append({
            'interaction_id': interaction['interaction_id'],
            'timestamp': interaction['timestamp'],
            'type': interaction['interaction_type'],
            'function': interaction.get('function_name', ''),
            'input_tokens': interaction['input_tokens'],
            'output_tokens': interaction['output_tokens'],
            'total_tokens': interaction['total_tokens'],
            'duration_ms': interaction.get('duration_ms', 0)
        })
    return pd.DataFrame(data)

# 导出为CSV
df = to_dataframe(interactions)
df.to_csv('llm_interactions_analysis.csv', index=False)
```

## 📝 注意事项

1. **存储空间**: 原始报文包含完整的API数据，文件可能较大
2. **隐私安全**: 日志文件包含用户查询和系统响应，注意数据安全
3. **性能影响**: 文件写入操作对性能影响很小，但大量交互时需注意磁盘空间
4. **文件管理**: 建议定期清理旧的日志文件，或实现自动归档机制

## 🎉 总结

LLM交互原始报文保存功能为SQLite Agent提供了完整的可观测性，帮助开发者：
- 深入了解LLM的工作机制
- 优化性能和成本
- 调试和改进Agent行为
- 建立完整的审计追踪

通过结合日志查看器和自定义分析脚本，可以充分利用这些数据来持续改进Agent的表现。
