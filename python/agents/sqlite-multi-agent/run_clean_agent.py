#!/usr/bin/env python3
"""
简洁版SQLite多agent系统运行脚本
避免多次执行查询，提供清晰的交互体验
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载.env文件
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    print("⚠️  未安装python-dotenv，使用环境变量")

from sqlite_agent.agent import sqlite_agent
from google.adk.runners import InMemoryRunner


async def run_clean_agent():
    """简洁版多agent系统运行"""
    print("🚀 SQLite多agent系统")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 未找到DEEPSEEK_API_KEY")
        print("请在 .env 文件中设置或使用:")
        print("   export DEEPSEEK_API_KEY='your-api-key'")
        return
    
    # 显示配置
    print("📋 系统配置:")
    print(f"   ✅ API Key: 已设置")
    print(f"   🤖 Model: {os.getenv('MODEL_NAME', 'deepseek/deepseek-chat')}")
    print(f"   💾 Database: {os.getenv('SQLITE_DATABASE_PATH', 'data/sample.db')}")
    
    try:
        # 创建runner和session
        runner = InMemoryRunner(
            app_name="sqlite_multi_agent_clean",
            agent=sqlite_agent
        )
        
        session = await runner.session_service.create_session(
            app_name="sqlite_multi_agent_clean",
            user_id="user"
        )
        
        print(f"\n✅ 系统启动成功！")
        print(f"🎯 主agent: {sqlite_agent.name}")
        print(f"📊 子agent数量: {len(sqlite_agent.sub_agents)}")
        
        print("\n🤖 可用的子agents:")
        for i, sub_agent in enumerate(sqlite_agent.sub_agents, 1):
            print(f"   {i}. {sub_agent.name}")
        
        print("\n💡 示例查询:")
        examples = [
            "有哪些表？",
            "显示orders表的结构", 
            "显示orders表的样本数据",
            "显示orders表中最贵的订单记录",
            "查询用户总数"
        ]
        
        for i, example in enumerate(examples, 1):
            print(f"   {i}. {example}")
        
        print("\n🎉 系统已准备就绪！")
        
        # 交互循环
        print("\n" + "=" * 50)
        print("💭 请输入你的问题 (输入数字选择示例，或 'quit' 退出):")
        
        query_count = 0
        
        while True:
            try:
                user_input = input(f"\n[查询 #{query_count + 1}] 🤔 你的问题: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                # 处理数字选择
                if user_input.isdigit():
                    idx = int(user_input) - 1
                    if 0 <= idx < len(examples):
                        user_input = examples[idx]
                        print(f"📝 选择的查询: {user_input}")
                    else:
                        print("❌ 无效的选择")
                        continue
                else:
                    print(f"📝 收到查询: {user_input}")
                
                query_count += 1
                print(f"⏳ 正在处理查询 #{query_count}...")
                
                try:
                    # 创建消息内容
                    from google.genai import types
                    content = types.Content(
                        role="user",
                        parts=[types.Part(text=user_input)]
                    )
                    
                    # 执行查询并实时显示响应
                    print("🤖 系统回答:")
                    print("-" * 40)
                    
                    response_parts = []
                    async for event in runner.run_async(
                        user_id="user",
                        session_id=session.id,
                        new_message=content
                    ):
                        if event.content and event.content.parts:
                            for part in event.content.parts:
                                if part.text:
                                    print(part.text, end='', flush=True)
                                    response_parts.append(part.text)
                    
                    print("\n" + "-" * 40)
                    
                    # 显示查询统计
                    total_response = "".join(response_parts)
                    print(f"📊 查询统计: 响应长度 {len(total_response)} 字符")
                    
                except Exception as e:
                    print(f"❌ 查询执行失败: {e}")
                    print("💡 可能的原因:")
                    print("   - API密钥无效或已过期")
                    print("   - 网络连接问题")
                    print("   - 数据库文件不存在")
                    print("   - 模型服务暂时不可用")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 处理错误: {e}")
        
        print(f"\n📈 会话统计: 总共执行了 {query_count} 次查询")
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print("💡 请检查:")
        print("   - API密钥是否正确")
        print("   - 网络连接是否正常")
        print("   - 依赖包是否完整安装")


async def main():
    """主函数"""
    print("🌟 SQLite多agent系统 - 简洁版")
    print("🎯 特点: 避免重复查询，提供清晰的交互体验")
    print()
    
    await run_clean_agent()


if __name__ == "__main__":
    asyncio.run(main())
