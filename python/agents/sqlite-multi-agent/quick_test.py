#!/usr/bin/env python3
"""
快速测试修改后的token追踪功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlite_agent.agent import sqlite_agent
from sqlite_agent.llm_monitor import enable_monitoring


async def test_real_token_tracking():
    """测试真实的token追踪"""
    
    print("🚀 启动SQLite Agent进行token追踪测试...")
    
    # 启用监控
    enable_monitoring(enable_detailed_logging=True, save_to_file=False)
    
    try:
        # 发送一个简单的查询
        response = await sqlite_agent.run_async("显示数据库中有哪些表")
        
        print(f"\n✅ Agent响应: {response}")
        print("\n💡 请查看上面的日志，寻找 '✅ (真实API返回)' 或 '⚠️ (本地估算)' 标识")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        print("💡 这可能是因为数据库连接或API配置问题")
    
    print("\n🎯 测试完成!")


if __name__ == "__main__":
    print("开始快速测试...")
    asyncio.run(test_real_token_tracking())
