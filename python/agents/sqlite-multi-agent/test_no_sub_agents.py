#!/usr/bin/env python3
"""
测试禁用子Agent模式后的sqlite-agent功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlite_agent.agent import sqlite_agent
from sqlite_agent.llm_monitor import enable_monitoring


async def test_no_sub_agents():
    """测试禁用子Agent模式的功能"""
    
    print("=" * 80)
    print("测试禁用子Agent模式的SQLite Agent")
    print("=" * 80)
    
    # 启用监控
    enable_monitoring(detailed_logging=True, save_to_file=False)
    
    test_queries = [
        "显示数据库中有哪些表",
        "显示orders表的结构信息", 
        "显示orders表中最贵的订单记录",
        "统计orders表中有多少条记录"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"测试查询 {i}: {query}")
        print(f"{'='*60}")
        
        try:
            response = await sqlite_agent.run_async(query)
            print(f"\n✅ 查询结果:")
            print(response)
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
        
        print(f"\n{'='*60}")
        print("等待2秒后继续下一个查询...")
        print(f"{'='*60}")
        await asyncio.sleep(2)
    
    print(f"\n{'='*80}")
    print("所有测试查询完成")
    print(f"{'='*80}")


def analyze_expected_behavior():
    """分析预期的行为变化"""
    
    print("\n" + "=" * 80)
    print("预期行为分析")
    print("=" * 80)
    
    print("🔧 修改内容:")
    print("   1. 设置 ENABLE_SUB_AGENTS = False")
    print("   2. 移除子Agent工具 (sqlite_exploration_agent, sqlite_query_agent, sqlite_analysis_agent)")
    print("   3. 只保留基础工具 (list_tables, get_table_info, execute_query, get_sample_data)")
    print("   4. 更新系统提示，移除子Agent相关说明")
    
    print("\n📈 预期改进:")
    print("   ✅ 减少交互轮次: 直接使用基础工具，避免Agent间协调")
    print("   ✅ 降低Token消耗: 减少不必要的中间步骤")
    print("   ✅ 提高响应速度: 避免多层Agent调用的延迟")
    print("   ✅ 简化执行流程: 主Agent直接执行，逻辑更清晰")
    print("   ✅ 避免重复查询: 消除Agent间状态同步问题")
    
    print("\n🎯 对比分析:")
    print("   修改前: 主Agent -> 子Agent -> 工具 -> 结果 -> 主Agent -> 最终回答")
    print("   修改后: 主Agent -> 工具 -> 结果 -> 最终回答")
    
    print("\n⚠️  注意事项:")
    print("   - 主Agent需要直接处理所有查询逻辑")
    print("   - 智能工具集需要确保工具选择的准确性")
    print("   - 可能需要更详细的工具使用指导")


def check_configuration():
    """检查配置是否正确"""
    
    print("\n" + "=" * 80)
    print("配置检查")
    print("=" * 80)
    
    # 检查ENABLE_SUB_AGENTS设置
    from sqlite_agent.agent import ENABLE_SUB_AGENTS
    print(f"📋 ENABLE_SUB_AGENTS: {ENABLE_SUB_AGENTS}")
    
    if ENABLE_SUB_AGENTS:
        print("❌ 子Agent模式仍然启用，请检查配置")
        return False
    else:
        print("✅ 子Agent模式已禁用")
    
    # 检查Agent工具配置
    tools = sqlite_agent.tools
    tool_names = []
    
    for tool in tools:
        if hasattr(tool, 'name'):
            tool_names.append(tool.name)
        elif hasattr(tool, 'agent') and hasattr(tool.agent, 'name'):
            tool_names.append(f"AgentTool({tool.agent.name})")
        else:
            tool_names.append(str(type(tool).__name__))
    
    print(f"📋 可用工具: {', '.join(tool_names)}")
    
    # 检查是否还有子Agent工具
    sub_agent_tools = [name for name in tool_names if 'AgentTool' in name]
    if sub_agent_tools:
        print(f"❌ 仍然存在子Agent工具: {', '.join(sub_agent_tools)}")
        return False
    else:
        print("✅ 已移除所有子Agent工具")
    
    return True


if __name__ == "__main__":
    print("开始测试禁用子Agent模式的SQLite Agent...")
    
    # 检查配置
    config_ok = check_configuration()
    
    if not config_ok:
        print("\n❌ 配置检查失败，请检查修改是否正确")
        sys.exit(1)
    
    # 分析预期行为
    analyze_expected_behavior()
    
    # 运行测试
    print("\n🚀 开始运行测试查询...")
    asyncio.run(test_no_sub_agents())
    
    print("\n🎉 测试完成!")
    print("💡 请观察日志中的交互次数和Token消耗是否有所减少")
    print("📊 预期每个查询的交互轮次应该显著减少")
