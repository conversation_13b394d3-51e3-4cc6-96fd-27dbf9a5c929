# SQLite Agent

A powerful SQLite database assistant built with Google ADK (Agent Development Kit) that enables natural language querying and analysis of SQLite databases.

## 🌟 Features

### 🔍 **Intelligent Database Querying**
- Natural language to SQL conversion
- Safe query execution with built-in validation
- Automatic result formatting and presentation
- Support for complex analytical queries

### 🛡️ **Security & Safety**
- Read-only database access by default
- Query validation to prevent dangerous operations
- SQL injection protection
- Automatic query timeouts

### 🛠️ **Comprehensive Tools**
- **Table Discovery**: List and explore database tables
- **Schema Analysis**: Detailed table structure information
- **Data Sampling**: Preview table contents
- **Query Execution**: Safe SELECT query processing

### 🎯 **User-Friendly**
- Conversational interface
- Clear error messages and suggestions
- Formatted results with tables
- Query optimization recommendations

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- DeepSeek API Key
- Poetry (recommended) or pip

### Installation

1. **<PERSON>lone and navigate to the project:**
   ```bash
   cd /path/to/adk-samples/python/agents/sqlite-agent
   ```

2. **Install dependencies:**
   ```bash
   poetry install
   # or
   pip install -e .
   ```

3. **Configure your environment:**
   ```bash
   export DEEPSEEK_API_KEY="your-deepseek-api-key"
   export MODEL_NAME="deepseek/deepseek-chat"
   export SQLITE_DATABASE_PATH="data/sample.db"
   ```

### Basic Usage

```python
from sqlite_agent import sqlite_agent
from google.adk.runners import InMemoryRunner

# Create and start the agent
runner = InMemoryRunner(
    app_name="sqlite_agent",
    agent=sqlite_agent
)

# Run deployment script
python deployment/deploy.py
```

## 📖 Examples

### Interactive Usage

Run the basic example for an interactive session:

```bash
python examples/basic_usage.py
```

Example queries you can try:
- "What tables are available?"
- "Show me the employees table structure"
- "Who are the highest paid employees?"
- "What's the average salary by department?"

### Advanced Analytics

Run advanced examples with complex queries:

```bash
python examples/advanced_usage.py
```

This demonstrates:
- Multi-table joins
- Aggregation and grouping
- Time-series analysis
- Business intelligence queries

### 🎯 Hybrid Strategy Demo

Experience the intelligent tool selection:

```bash
python examples/hybrid_strategy_demo.py
```

This showcases:
- **Smart tool selection** based on user intent
- **Specialized sub-agents** for complex tasks
- **Context-aware** tool filtering
- **Performance optimization** through selective tool exposure

## 🏗️ Architecture

### 🎯 混合策略架构

The SQLite Agent uses a **混合策略架构** that combines multiple tool selection approaches:

#### 🧠 智能工具选择层
- **基础工具**: 向后兼容的直接工具访问
- **智能工具集**: 基于用户意图的动态工具选择
- **上下文感知**: 根据会话历史调整工具选择

#### 🤖 专门子Agent层
- **探索Agent**: 专门处理数据库结构探索
- **查询Agent**: 专门处理数据查询和检索
- **分析Agent**: 专门处理数据分析和洞察

#### 📁 项目结构
```
sqlite-agent/
├── sqlite_agent/           # Core agent package
│   ├── agent.py           # Main agent with hybrid strategy
│   ├── tools.py           # Basic SQLite operation tools
│   ├── smart_toolset.py   # Intelligent tool selection
│   ├── sub_agents.py      # Specialized sub-agents
│   ├── config.py          # Configuration management
│   ├── prompts.py         # Agent instructions
│   └── utils.py           # Utility functions
├── examples/              # Usage examples
│   └── hybrid_strategy_demo.py  # Hybrid strategy demo
├── tests/                 # Test suite
├── deployment/            # Deployment scripts
└── eval/                  # Evaluation framework
```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DEEPSEEK_API_KEY` | - | **Required**: Your DeepSeek API key |
| `MODEL_NAME` | `deepseek/deepseek-chat` | LLM model to use |
| `SQLITE_DATABASE_PATH` | `data/sample.db` | Path to SQLite database |
| `SQLITE_MAX_RESULTS` | `100` | Maximum query results |
| `SQLITE_TIMEOUT` | `30` | Query timeout (seconds) |
| `SQLITE_READ_ONLY` | `true` | Read-only database access |

### Custom Database

To use your own database:

1. Set the database path:
   ```bash
   export SQLITE_DATABASE_PATH="/path/to/your/database.db"
   ```

2. Ensure the database is readable:
   ```bash
   chmod 644 /path/to/your/database.db
   ```

## 🛡️ Security Features

### Query Validation
- Only `SELECT` statements allowed
- Blocks dangerous keywords (`INSERT`, `UPDATE`, `DELETE`, etc.)
- Prevents SQL injection attempts
- Validates query syntax

### Access Control
- Read-only database connections
- Configurable result limits
- Query timeouts
- Error sanitization

## 🧪 Testing

Run the test suite:

```bash
# Using pytest
pytest tests/

# Using poetry
poetry run pytest tests/

# With coverage
pytest tests/ --cov=sqlite_agent
```

## 📊 Sample Database

The agent comes with a sample database containing:

- **employees**: Employee information (name, department, salary, hire_date)
- **departments**: Department details (name, manager, budget)

Perfect for testing and learning!

## 🚀 Deployment

### Local Development

```bash
python deployment/deploy.py
```

### Local Deployment

1. **Set up environment:**
   ```bash
   export DEEPSEEK_API_KEY="your-deepseek-api-key"
   export SQLITE_DATABASE_PATH="/path/to/your/database.db"
   ```

2. **Deploy locally:**
   ```bash
   python deployment/deploy.py
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0. See the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check the examples and code comments
- **Issues**: Report bugs and request features via GitHub issues
- **Community**: Join the ADK community discussions

---

**Built with ❤️ using Google ADK**
