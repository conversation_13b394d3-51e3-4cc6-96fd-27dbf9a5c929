#!/usr/bin/env python3
"""
真实运行SQLite多agent系统
实际调用DeepSeek API执行查询
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载.env文件
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    print("⚠️  未安装python-dotenv，使用环境变量")

from sqlite_agent.agent import sqlite_agent
from google.adk.runners import InMemoryRunner


async def run_real_agent():
    """真实运行多agent系统"""
    print("🚀 启动SQLite多agent系统 (真实模式)")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 未找到DEEPSEEK_API_KEY")
        print("请在 .env 文件中设置或使用:")
        print("   export DEEPSEEK_API_KEY='your-api-key'")
        return
    
    # 显示配置
    print("📋 当前配置:")
    print(f"   API Key: {'已设置 (' + api_key[:8] + '...)' if api_key else '未设置'}")
    print(f"   Model: {os.getenv('MODEL_NAME', 'deepseek/deepseek-chat')}")
    print(f"   Database: {os.getenv('SQLITE_DATABASE_PATH', 'data/sample.db')}")
    
    try:
        # 创建runner
        runner = InMemoryRunner(
            app_name="sqlite_multi_agent_real",
            agent=sqlite_agent
        )

        # 创建session
        session = await runner.session_service.create_session(
            app_name="sqlite_multi_agent_real",
            user_id="real_user"
        )

        print("\n✅ SQLite多agent系统启动成功！")
        print(f"🤖 主agent: {sqlite_agent.name}")
        print(f"📊 子agent数量: {len(sqlite_agent.sub_agents)}")

        print("\n🎯 子agent列表:")
        for i, sub_agent in enumerate(sqlite_agent.sub_agents, 1):
            print(f"   {i}. {sub_agent.name}")

        print("\n💡 示例查询:")
        examples = [
            "有哪些表？",
            "显示employees表的结构",
            "显示employees表的样本数据",
            "查询所有员工信息",
            "显示orders表中最贵的订单记录"
        ]

        for i, example in enumerate(examples, 1):
            print(f"   {i}. {example}")

        print("\n🎉 系统已准备就绪！现在可以执行真实查询")

        # 交互循环
        print("\n" + "=" * 60)
        print("💭 输入你的问题 (输入数字选择示例，或输入 'quit' 退出):")
        
        while True:
            try:
                user_input = input("\n🤔 你的问题: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                # 处理数字选择
                if user_input.isdigit():
                    idx = int(user_input) - 1
                    if 0 <= idx < len(examples):
                        user_input = examples[idx]
                        print(f"📝 选择的查询: {user_input}")
                    else:
                        print("❌ 无效的选择")
                        continue
                else:
                    print(f"📝 收到查询: {user_input}")
                
                print("⏳ 正在处理查询...")
                print("🔄 主agent正在分析并选择合适的子agent...")

                try:
                    # 创建消息内容
                    from google.genai import types
                    content = types.Content(
                        role="user",
                        parts=[types.Part(text=user_input)]
                    )

                    # 实际执行查询并收集响应
                    response_parts = []
                    async for event in runner.run_async(
                        user_id="real_user",
                        session_id=session.id,
                        new_message=content
                    ):
                        if event.content and event.content.parts:
                            for part in event.content.parts:
                                if part.text:
                                    response_parts.append(part.text)

                    response = "".join(response_parts)
                    print(f"\n🤖 系统回答:")
                    print("-" * 40)
                    print(response)
                    print("-" * 40)
                    
                except Exception as e:
                    print(f"❌ 查询执行失败: {e}")
                    print("💡 可能的原因:")
                    print("   - API密钥无效或已过期")
                    print("   - 网络连接问题")
                    print("   - 数据库文件不存在")
                    print("   - 模型服务暂时不可用")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 处理错误: {e}")
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print("💡 请检查:")
        print("   - API密钥是否正确")
        print("   - 网络连接是否正常")
        print("   - 依赖包是否完整安装")


async def test_simple_query():
    """测试简单查询"""
    print("🧪 测试简单查询...")

    try:
        runner = InMemoryRunner(
            app_name="test_simple",
            agent=sqlite_agent
        )

        # 创建session
        session = await runner.session_service.create_session(
            app_name="test_simple",
            user_id="test_user"
        )

        # 测试最简单的查询
        test_query = "有哪些表？"
        print(f"📝 测试查询: {test_query}")

        # 创建消息内容
        from google.genai import types
        content = types.Content(
            role="user",
            parts=[types.Part(text=test_query)]
        )

        # 执行查询并收集响应
        response_parts = []
        async for event in runner.run_async(
            user_id="test_user",
            session_id=session.id,
            new_message=content
        ):
            if event.content and event.content.parts:
                for part in event.content.parts:
                    if part.text:
                        response_parts.append(part.text)

        response = "".join(response_parts)
        print(f"✅ 查询成功!")
        print(f"🤖 回答: {response}")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🌟 SQLite多agent系统 - 真实运行模式")
    print()
    
    # 先测试简单查询
    print("🔍 首先测试系统是否正常工作...")
    test_success = await test_simple_query()
    
    if test_success:
        print("\n✅ 系统测试通过，启动完整交互模式...")
        await run_real_agent()
    else:
        print("\n❌ 系统测试失败，请检查配置")
        print("💡 建议:")
        print("   1. 确认DEEPSEEK_API_KEY是否正确")
        print("   2. 检查网络连接")
        print("   3. 确认数据库文件存在")


if __name__ == "__main__":
    asyncio.run(main())
