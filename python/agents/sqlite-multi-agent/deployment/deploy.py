# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Deployment script for SQLite Agent."""

import os
from dotenv import load_dotenv

from google.adk.runners import Runner
from sqlite_agent import sqlite_agent

# Load environment variables
load_dotenv()

async def run_interactive_session(runner):
    """Run interactive session with the agent."""

    session = await runner.session_service.create_session(
        app_name="sqlite_agent",
        user_id="user"
    )

    print("\n" + "="*50)
    print("SQLite Agent Interactive Session")
    print("Type 'quit' or 'exit' to end the session")
    print("="*50)

    try:
        while True:
            user_input = input("\n💬 You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            if not user_input:
                continue

            print("🤖 SQLite Agent:")

            # Create message content
            from google.genai import types
            content = types.Content(
                role="user",
                parts=[types.Part(text=user_input)]
            )

            # Stream the response
            try:
                async for event in runner.run_async(
                    user_id="user",
                    session_id=session.id,
                    new_message=content
                ):
                    if event.content and event.content.parts:
                        for part in event.content.parts:
                            if part.text:
                                print(part.text, end='', flush=True)
                print()  # New line after response
            except Exception as e:
                print(f"❌ Error: {str(e)}")

    except KeyboardInterrupt:
        print("\n👋 Session interrupted. Goodbye!")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    finally:
        # 打印会话总结
        from sqlite_agent.llm_monitor import print_session_summary
        print_session_summary()

def main():
    """Main deployment function."""

    # Check required environment variables
    required_vars = ["DEEPSEEK_API_KEY", "SQLITE_DATABASE_PATH"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print(f"Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these variables in your .env file or environment.")
        return

    # Create runner for local deployment
    from google.adk.runners import InMemoryRunner

    runner = InMemoryRunner(
        app_name="sqlite_agent",
        agent=sqlite_agent
    )

    print("Starting SQLite Agent...")
    print(f"Model: {os.getenv('MODEL_NAME', 'deepseek/deepseek-chat')}")
    print(f"Database: {os.getenv('SQLITE_DATABASE_PATH', 'data/sample.db')}")

    # Start interactive session
    import asyncio
    asyncio.run(run_interactive_session(runner))


if __name__ == "__main__":
    main()
