#!/usr/bin/env python3
"""
测试修改后的token追踪功能 - 只使用真实API数据，不进行本地估算
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlite_agent.llm_monitor import LLMMonitor


class MockUsageMetadata:
    """模拟usage_metadata对象"""
    
    def __init__(self, prompt_tokens: int, completion_tokens: int, total_tokens: int):
        self.prompt_token_count = prompt_tokens
        self.candidates_token_count = completion_tokens
        self.total_token_count = total_tokens


def test_no_estimation_policy():
    """测试不进行本地估算的策略"""
    
    print("=" * 80)
    print("测试Token追踪功能 - 只使用真实API数据")
    print("=" * 80)
    
    # 创建监控器
    monitor = LLMMonitor(enable_detailed_logging=True, save_to_file=False)
    
    # 测试用例1：有真实API token数据
    print("\n🧪 测试用例1：有真实API token数据")
    usage_metadata = MockUsageMetadata(prompt_tokens=100, completion_tokens=20, total_tokens=120)
    
    interaction1 = monitor.log_interaction(
        interaction_type="function_call",
        input_content="测试输入内容",
        output_content="Function Call: test_function({})",
        function_name="test_function",
        function_args={},
        usage_metadata=usage_metadata
    )
    
    print(f"✅ Token来源: {interaction1.token_source}")
    print(f"📊 Token数据: 输入{interaction1.input_tokens}, 输出{interaction1.output_tokens}, 总计{interaction1.total_tokens}")
    
    # 测试用例2：从raw_response中提取token数据
    print("\n🧪 测试用例2：从raw_response中提取token数据")
    raw_response_with_usage = {
        "usage": {
            "prompt_tokens": 150,
            "completion_tokens": 30,
            "total_tokens": 180
        }
    }
    
    interaction2 = monitor.log_interaction(
        interaction_type="final_answer",
        input_content="另一个测试输入内容",
        output_content="这是一个测试回答",
        raw_response=raw_response_with_usage
    )
    
    print(f"✅ Token来源: {interaction2.token_source}")
    print(f"📊 Token数据: 输入{interaction2.input_tokens}, 输出{interaction2.output_tokens}, 总计{interaction2.total_tokens}")
    
    # 测试用例3：没有token数据 - 不进行本地估算
    print("\n🧪 测试用例3：没有token数据 - 不进行本地估算")
    interaction3 = monitor.log_interaction(
        interaction_type="tool_selection",
        input_content="没有usage信息的测试输入",
        output_content="没有usage信息的测试输出"
    )
    
    print(f"⚠️  Token来源: {interaction3.token_source}")
    print(f"📊 Token数据: 输入{interaction3.input_tokens}, 输出{interaction3.output_tokens}, 总计{interaction3.total_tokens}")
    
    # 测试用例4：工具响应 - 通常没有token数据
    print("\n🧪 测试用例4：工具响应 - 通常没有token数据")
    interaction4 = monitor.log_interaction(
        interaction_type="function_response",
        input_content="工具调用结果",
        output_content="数据库查询结果: [{'id': 1, 'name': 'test'}]"
    )
    
    print(f"⚠️  Token来源: {interaction4.token_source}")
    print(f"📊 Token数据: 输入{interaction4.input_tokens}, 输出{interaction4.output_tokens}, 总计{interaction4.total_tokens}")
    
    # 打印会话总结
    print("\n" + "=" * 80)
    print("会话总结")
    print("=" * 80)
    monitor.print_session_summary()
    
    # 验证统计数据
    stats = monitor.session_stats
    print(f"\n🔍 验证结果:")
    print(f"   总交互次数: {stats.total_interactions}")
    print(f"   总Token消耗: {stats.total_tokens} (应该只包含前2个交互的token)")
    print(f"   预期Token总计: {120 + 180} = 300")
    print(f"   实际Token总计: {stats.total_tokens}")
    print(f"   ✅ 验证通过: {stats.total_tokens == 300}")
    
    return stats.total_tokens == 300


def test_json_output():
    """测试JSON输出格式"""
    
    print("\n" + "=" * 80)
    print("测试JSON输出格式")
    print("=" * 80)
    
    # 创建监控器，启用文件保存
    monitor = LLMMonitor(enable_detailed_logging=False, save_to_file=True, output_dir="test_logs")
    
    # 添加一个有token数据的交互
    usage_metadata = MockUsageMetadata(prompt_tokens=50, completion_tokens=10, total_tokens=60)
    monitor.log_interaction(
        interaction_type="function_call",
        input_content="测试JSON输出",
        output_content="Function Call: test()",
        usage_metadata=usage_metadata
    )
    
    # 添加一个没有token数据的交互
    monitor.log_interaction(
        interaction_type="function_response",
        input_content="工具响应",
        output_content="响应结果"
    )
    
    print(f"📁 日志文件: {monitor.log_file}")
    print("💡 请检查日志文件中的token_source字段")
    
    return True


if __name__ == "__main__":
    print("开始测试不进行本地估算的Token追踪功能...")
    
    # 运行测试
    test1_passed = test_no_estimation_policy()
    test2_passed = test_json_output()
    
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    print(f"✅ 不进行本地估算测试: {'通过' if test1_passed else '失败'}")
    print(f"✅ JSON输出格式测试: {'通过' if test2_passed else '失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过!")
        print("💡 现在sqlite-agent只会记录真实的LLM API token数据")
        print("⚠️  没有token数据的交互将显示为'无法获取'")
    else:
        print("\n❌ 部分测试失败，请检查代码")
    
    print("\n🚀 可以运行 sqlite-agent 查看实际效果!")
