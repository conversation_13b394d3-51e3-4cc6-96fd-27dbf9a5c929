[tool.poetry]
name = "sqlite-agent"
version = "0.1.0"
description = "SQLite Database Agent using Google ADK"
authors = ["ADK Team <<EMAIL>>"]
license = "Apache License 2.0"
readme = "README.md"
package-mode = true

[tool.poetry.dependencies]
python = "^3.12"
python-dotenv = "^1.0.1"
google-adk = "^1.5.0"
pydantic = "^2.11.3"
tabulate = "^0.9.0"
google-cloud-aiplatform = { extras = [
    "adk",
    "agent-engines",
], version = "^1.93.0" }
absl-py = "^2.2.2"
litellm = "^1.0.0"

[tool.poetry.group.dev.dependencies]
google-cloud-aiplatform = { extras = [
    "adk",
    "agent-engines",
    "evaluation",
], version = "^1.93.0" }
pytest = "^8.3.5"
pytest-asyncio = "^0.26.0"
google-adk = { extras = ["eval"], version = "^1.5.0" }

[tool.pytest.ini_options]
console_output_style = "progress"
addopts = "-vv -s"
testpaths = ["tests/"]
log_level = "DEBUG"
log_cli = true
log_auto_indent = true
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
log_cli_format = "[%(asctime)s] %(levelname)s (%(funcName)s) \t [%(pathname)s:%(lineno)d] %(message)s"
filterwarnings = [
    "ignore::UserWarning",
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
