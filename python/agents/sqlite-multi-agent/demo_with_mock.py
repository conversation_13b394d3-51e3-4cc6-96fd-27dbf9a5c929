#!/usr/bin/env python3
"""
SQLite多agent系统模拟演示
展示系统如何路由查询到不同的子agent
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 尝试加载.env文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from sqlite_agent.agent import sqlite_agent


def analyze_query_intent(query: str) -> tuple[str, str]:
    """分析查询意图，返回(agent_name, explanation)"""
    query_lower = query.lower()
    
    # 列表查询
    if any(keyword in query_lower for keyword in ["表", "table"]) and \
       any(keyword in query_lower for keyword in ["有哪些", "列出", "list", "show tables"]):
        return "list_tables_agent", "需要列出数据库中的所有表"
    
    # 表结构查询
    elif any(keyword in query_lower for keyword in ["结构", "字段", "schema", "info", "describe", "desc"]):
        return "table_info_agent", "需要获取表的结构信息"
    
    # 样本数据查询
    elif any(keyword in query_lower for keyword in ["样本", "示例", "sample", "例子", "preview"]):
        return "sample_data_agent", "需要获取表的样本数据"
    
    # SQL查询
    elif any(keyword in query_lower for keyword in ["查询", "select", "最", "统计", "count", "sum", "avg", "max", "min", "where", "order by"]):
        return "query_execution_agent", "需要执行SQL查询来获取特定数据"
    
    else:
        return "主agent协调", "需要主agent分析并选择最合适的子agent"


def mock_agent_response(agent_name: str, query: str) -> str:
    """模拟不同agent的响应"""
    
    if agent_name == "list_tables_agent":
        return """
📋 数据库中的表列表：

1. **employees** - 员工信息表
2. **departments** - 部门信息表  
3. **orders** - 订单信息表
4. **products** - 产品信息表
5. **customers** - 客户信息表

💡 建议下一步操作：
- 查看表结构：使用 table_info_agent
- 查看样本数据：使用 sample_data_agent
"""
    
    elif agent_name == "table_info_agent":
        return """
## 表结构：orders

**基本信息：**
- 表名：orders
- 记录数：1,250 条

**字段详情：**
| 字段名 | 数据类型 | 非空 | 默认值 | 主键 | 说明 |
|--------|----------|------|--------|------|------|
| id     | INTEGER  | YES  |        | YES  | 订单唯一标识 |
| customer_id | INTEGER | YES |     | NO   | 客户ID |
| product_id  | INTEGER | YES |     | NO   | 产品ID |
| quantity    | INTEGER | YES |     | NO   | 订购数量 |
| price       | DECIMAL | YES |     | NO   | 单价 |
| total_amount| DECIMAL | YES |     | NO   | 总金额 |
| order_date  | DATE    | YES |     | NO   | 订单日期 |

**设计特点：**
- 使用自增主键id作为唯一标识
- total_amount = quantity * price
- 包含外键关联到customers和products表
"""
    
    elif agent_name == "sample_data_agent":
        return """
## 表 'orders' 的样本数据

**样本记录（前5条）：**
| id | customer_id | product_id | quantity | price | total_amount | order_date |
|----|-------------|------------|----------|-------|--------------|------------|
| 1  | 101         | 201        | 2        | 299.99| 599.98       | 2024-01-15 |
| 2  | 102         | 203        | 1        | 1299.99| 1299.99     | 2024-01-16 |
| 3  | 103         | 202        | 3        | 89.99 | 269.97       | 2024-01-17 |
| 4  | 101         | 204        | 1        | 2499.99| 2499.99     | 2024-01-18 |
| 5  | 104         | 201        | 5        | 299.99| 1499.95      | 2024-01-19 |

**数据特征分析：**
- 总记录数：1,250 条
- 价格范围：$89.99 - $2,499.99
- 订单金额范围：$269.97 - $2,499.99
- 数据完整性：所有字段都有值，无空值

**数据质量评估：**
✅ 无空值
✅ 价格格式正确
✅ 日期格式统一
✅ 数量都为正整数
"""
    
    elif agent_name == "query_execution_agent":
        if "最贵" in query or "max" in query.lower():
            return """
**执行的SQL查询：**
```sql
SELECT id, customer_id, product_id, quantity, price, total_amount, order_date
FROM orders 
ORDER BY total_amount DESC 
LIMIT 1;
```

**查询结果：**
| id | customer_id | product_id | quantity | price | total_amount | order_date |
|----|-------------|------------|----------|-------|--------------|------------|
| 847| 156         | 301        | 2        | 4999.99| 9999.98     | 2024-02-28 |

**结果分析：**
- 最贵订单ID：847
- 客户ID：156
- 产品ID：301 (可能是高端产品)
- 订购数量：2件
- 单价：$4,999.99
- 总金额：$9,999.98
- 订单日期：2024-02-28

**业务洞察：**
- 这是一个高价值订单，总金额接近$10,000
- 客户156是重要客户，值得重点关注
- 产品301可能是旗舰产品，利润率较高

**建议后续查询：**
- 查看客户156的其他订单：`SELECT * FROM orders WHERE customer_id = 156`
- 查看产品301的详细信息：`SELECT * FROM products WHERE id = 301`
"""
        else:
            return "🔍 query_execution_agent 会根据具体查询构建和执行相应的SQL语句"
    
    else:
        return "🤖 主agent正在分析查询并选择最合适的子agent..."


async def demo_with_mock():
    """模拟演示多agent系统"""
    print("🎯 SQLite多agent系统模拟演示")
    print("=" * 60)
    print("💡 这个演示展示了系统如何智能路由查询到不同的子agent")
    print()
    
    # 预定义的示例查询
    sample_queries = [
        "有哪些表？",
        "显示orders表的结构",
        "显示orders表的样本数据", 
        "显示orders表中最贵的订单记录",
        "查询员工总数",
        "显示所有部门信息"
    ]
    
    print("📋 预定义示例查询:")
    for i, query in enumerate(sample_queries, 1):
        print(f"   {i}. {query}")
    
    print("\n" + "=" * 60)
    print("💭 输入你的问题 (输入数字选择示例，或输入 'quit' 退出):")
    
    while True:
        try:
            user_input = input("\n🤔 你的问题: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            # 处理数字选择
            if user_input.isdigit():
                idx = int(user_input) - 1
                if 0 <= idx < len(sample_queries):
                    user_input = sample_queries[idx]
                    print(f"📝 选择的查询: {user_input}")
                else:
                    print("❌ 无效的选择")
                    continue
            else:
                print(f"📝 收到查询: {user_input}")
            
            # 分析查询意图
            agent_name, explanation = analyze_query_intent(user_input)
            print(f"🎯 分析结果: {explanation}")
            print(f"🤖 选择的agent: {agent_name}")
            
            print("\n⏳ 正在处理...")
            await asyncio.sleep(1)  # 模拟处理时间
            
            # 生成模拟响应
            response = mock_agent_response(agent_name, user_input)
            print(f"\n🤖 {agent_name} 的回答:")
            print(response)
            
            print("\n" + "-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 处理错误: {e}")


if __name__ == "__main__":
    asyncio.run(demo_with_mock())
