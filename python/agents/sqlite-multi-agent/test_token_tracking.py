#!/usr/bin/env python3
"""
测试真实token使用量追踪功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlite_agent.llm_monitor import LLMMonitor
from sqlite_agent.monitored_llm import create_monitored_llm
from sqlite_agent.config import get_model_name


class MockUsageMetadata:
    """模拟usage_metadata对象"""
    
    def __init__(self, prompt_tokens: int, completion_tokens: int, total_tokens: int):
        self.prompt_token_count = prompt_tokens
        self.candidates_token_count = completion_tokens
        self.total_token_count = total_tokens


def test_token_source_detection():
    """测试token来源检测功能"""
    
    print("=" * 80)
    print("测试Token使用量追踪功能")
    print("=" * 80)
    
    # 创建监控器
    monitor = LLMMonitor(enable_detailed_logging=True, save_to_file=False)
    
    # 测试用例1：使用真实的usage_metadata
    print("\n🧪 测试用例1：真实API返回的token数据")
    usage_metadata = MockUsageMetadata(prompt_tokens=100, completion_tokens=20, total_tokens=120)
    
    interaction1 = monitor.log_interaction(
        interaction_type="function_call",
        input_content="测试输入内容，这里有一些文本用于测试token计算",
        output_content="Function Call: test_function({})",
        function_name="test_function",
        function_args={},
        usage_metadata=usage_metadata
    )
    
    print(f"✅ Token来源: {interaction1.token_source}")
    print(f"📊 Token数据: 输入{interaction1.input_tokens}, 输出{interaction1.output_tokens}, 总计{interaction1.total_tokens}")
    
    # 测试用例2：使用raw_response中的usage信息
    print("\n🧪 测试用例2：从raw_response中提取token数据")
    raw_response_with_usage = {
        "usage": {
            "prompt_tokens": 150,
            "completion_tokens": 30,
            "total_tokens": 180
        }
    }
    
    interaction2 = monitor.log_interaction(
        interaction_type="final_answer",
        input_content="另一个测试输入内容",
        output_content="这是一个测试回答",
        raw_response=raw_response_with_usage
    )
    
    print(f"✅ Token来源: {interaction2.token_source}")
    print(f"📊 Token数据: 输入{interaction2.input_tokens}, 输出{interaction2.output_tokens}, 总计{interaction2.total_tokens}")
    
    # 测试用例3：使用本地估算
    print("\n🧪 测试用例3：本地估算token数据")
    interaction3 = monitor.log_interaction(
        interaction_type="tool_selection",
        input_content="没有usage信息的测试输入",
        output_content="没有usage信息的测试输出"
    )
    
    print(f"⚠️  Token来源: {interaction3.token_source}")
    print(f"📊 Token数据: 输入{interaction3.input_tokens}, 输出{interaction3.output_tokens}, 总计{interaction3.total_tokens}")
    
    # 对比分析
    print("\n" + "=" * 80)
    print("对比分析")
    print("=" * 80)
    
    print(f"📈 真实API数据 vs 本地估算对比:")
    print(f"   用例1 (真实): 输入{interaction1.input_tokens}, 输出{interaction1.output_tokens}, 总计{interaction1.total_tokens}")
    print(f"   用例3 (估算): 输入{interaction3.input_tokens}, 输出{interaction3.output_tokens}, 总计{interaction3.total_tokens}")
    
    # 计算差异
    if interaction1.total_tokens > 0 and interaction3.total_tokens > 0:
        diff_percentage = abs(interaction1.total_tokens - interaction3.total_tokens) / interaction1.total_tokens * 100
        print(f"   📊 差异百分比: {diff_percentage:.1f}%")
    
    print("\n✅ 测试完成!")
    print("💡 现在LLM日志将显示真实的API token使用量，而不是本地估算值")


def test_integration():
    """测试与实际LLM的集成"""
    
    print("\n" + "=" * 80)
    print("集成测试说明")
    print("=" * 80)
    
    print("🔧 修改内容:")
    print("   1. LLMMonitor.log_interaction() 新增 usage_metadata 参数")
    print("   2. monitored_llm.py 从 LlmResponse.usage_metadata 提取真实token数据")
    print("   3. 日志显示中添加token来源标识 (✅真实API返回 / ⚠️本地估算)")
    print("   4. 优先级: usage_metadata > raw_response.usage > 本地估算")
    
    print("\n📋 使用方法:")
    print("   - 运行 sqlite-agent 时，日志将自动显示真实的token使用量")
    print("   - 在日志中查找 '✅ (真实API返回)' 标识确认使用真实数据")
    print("   - 如果看到 '⚠️ (本地估算)' 说明API没有返回usage信息")
    
    print("\n🎯 预期效果:")
    print("   - DeepSeek API 返回的真实token数量将被准确记录")
    print("   - 不再依赖可能不准确的本地tiktoken估算")
    print("   - 提供更精确的token使用量统计和成本计算")


if __name__ == "__main__":
    print("开始测试Token使用量追踪功能...")
    
    # 运行测试
    test_token_source_detection()
    test_integration()
    
    print("\n🚀 可以运行 sqlite-agent 查看实际效果!")
