#!/usr/bin/env python3
"""
使用.env文件运行SQLite多agent系统
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 尝试加载.env文件
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载 .env 文件")
except ImportError:
    print("⚠️  未安装python-dotenv，请手动设置环境变量")
    print("   pip install python-dotenv")

from sqlite_agent.agent import sqlite_agent
from google.adk.runners import InMemoryRunner


async def run_with_env():
    """使用环境变量运行系统"""
    print("🚀 启动SQLite多agent系统...")
    print("=" * 50)
    
    # 检查必要的环境变量
    required_vars = ["DEEPSEEK_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ 缺少必要的环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请在 .env 文件中设置这些变量，或使用:")
        for var in missing_vars:
            print(f"   export {var}='your-value'")
        return
    
    # 显示当前配置
    print("📋 当前配置:")
    print(f"   API Key: {'已设置' if os.getenv('DEEPSEEK_API_KEY') else '未设置'}")
    print(f"   Model: {os.getenv('MODEL_NAME', 'deepseek/deepseek-chat')}")
    print(f"   Database: {os.getenv('SQLITE_DATABASE_PATH', 'data/sample.db')}")
    print(f"   Max Results: {os.getenv('SQLITE_MAX_RESULTS', '100')}")
    print(f"   Read Only: {os.getenv('SQLITE_READ_ONLY', 'true')}")
    
    try:
        # 创建runner
        runner = InMemoryRunner(
            app_name="sqlite_multi_agent_with_env",
            agent=sqlite_agent
        )
        
        print("\n✅ SQLite多agent系统启动成功！")
        print(f"🤖 主agent: {sqlite_agent.name}")
        print(f"📊 子agent数量: {len(sqlite_agent.sub_agents)}")
        
        print("\n🎯 子agent列表:")
        for i, sub_agent in enumerate(sqlite_agent.sub_agents, 1):
            print(f"   {i}. {sub_agent.name}")
            print(f"      └─ {sub_agent.description}")
        
        print("\n💡 示例查询:")
        examples = [
            "有哪些表？",
            "显示employees表的结构", 
            "显示employees表的样本数据",
            "查询所有员工的姓名和部门"
        ]
        
        for i, example in enumerate(examples, 1):
            print(f"   {i}. {example}")
        
        print("\n🎉 系统已准备就绪！")
        print("💬 你可以开始与多agent系统交互了")
        
        # 简单的交互循环
        print("\n" + "=" * 50)
        print("💭 输入你的问题 (输入 'quit' 退出):")
        
        while True:
            try:
                user_input = input("\n🤔 你的问题: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                print(f"\n📝 收到查询: {user_input}")
                
                # 分析查询类型并提示会使用的agent
                if any(keyword in user_input for keyword in ["表", "table"]) and any(keyword in user_input for keyword in ["有哪些", "列出", "list"]):
                    print("🎯 → 将使用 list_tables_agent 处理")
                elif any(keyword in user_input for keyword in ["结构", "字段", "schema", "info"]):
                    print("🎯 → 将使用 table_info_agent 处理")
                elif any(keyword in user_input for keyword in ["样本", "示例", "sample", "例子"]):
                    print("🎯 → 将使用 sample_data_agent 处理")
                elif any(keyword in user_input for keyword in ["查询", "SELECT", "数据", "信息"]):
                    print("🎯 → 将使用 query_execution_agent 处理")
                else:
                    print("🎯 → 主agent将分析并选择最合适的子agent")
                
                print("⏳ 正在处理...")
                
                # 实际运行查询（需要有效的API key）
                try:
                    # response = await runner.run(user_input)
                    # print(f"\n🤖 回答:\n{response}")
                    print("💡 提示: 需要有效的DEEPSEEK_API_KEY才能实际执行查询")
                except Exception as e:
                    print(f"❌ 查询执行失败: {e}")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 处理错误: {e}")
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print("💡 请检查配置和依赖是否正确")


if __name__ == "__main__":
    asyncio.run(run_with_env())
