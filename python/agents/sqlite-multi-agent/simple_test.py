#!/usr/bin/env python3
"""
简单测试工具筛选机制
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlite_agent.smart_toolset import SQLiteSmartToolset


class MockReadonlyContext:
    """模拟ReadonlyContext用于测试"""
    
    def __init__(self, user_query: str):
        self.user_content = MockContent(user_query)
        self.state = {}


class MockContent:
    """模拟Content对象"""
    
    def __init__(self, text: str):
        self.parts = [MockPart(text)]


class MockPart:
    """模拟Part对象"""
    
    def __init__(self, text: str):
        self.text = text


async def test_single_query():
    """测试单个查询"""
    
    smart_toolset = SQLiteSmartToolset()
    
    # 测试查询意图
    query = "查询用户数据"
    print(f"测试查询: '{query}'")
    
    context = MockReadonlyContext(query)
    tools = await smart_toolset.get_tools(context)
    tool_names = [tool.name for tool in tools]
    
    print(f"筛选后的工具: {tool_names}")
    print(f"预期的query工具: {smart_toolset.query_tools}")


if __name__ == "__main__":
    asyncio.run(test_single_query())
