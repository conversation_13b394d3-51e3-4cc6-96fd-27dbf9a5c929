#!/usr/bin/env python3
"""
运行SQLite多agent系统的简单脚本
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlite_agent.agent import sqlite_agent
from google.adk.runners import InMemoryRunner


async def run_sqlite_agent():
    """运行SQLite多agent系统"""
    print("🚀 启动SQLite多agent系统...")
    
    # 设置环境变量（如果未设置）
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️  请设置DEEPSEEK_API_KEY环境变量")
        return
    
    os.environ.setdefault("MODEL_NAME", "deepseek/deepseek-chat")
    os.environ.setdefault("SQLITE_DATABASE_PATH", "data/sample.db")
    
    try:
        # 创建runner
        runner = InMemoryRunner(
            app_name="sqlite_multi_agent",
            agent=sqlite_agent
        )
        
        print("✅ SQLite多agent系统启动成功！")
        print("\n📋 系统包含以下子agents:")
        for i, sub_agent in enumerate(sqlite_agent.sub_agents, 1):
            print(f"   {i}. {sub_agent.name}")
        
        print("\n💡 你可以询问:")
        print("   • 有哪些表？")
        print("   • 显示employees表的结构")
        print("   • 显示employees表的样本数据")
        print("   • 查询所有员工信息")
        
        # 这里可以添加交互逻辑
        print("\n🎯 系统已准备就绪，等待查询...")
        
        # 示例：处理一个简单查询
        # response = await runner.run("有哪些表？")
        # print(f"回答: {response}")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")


if __name__ == "__main__":
    asyncio.run(run_sqlite_agent())
