#!/usr/bin/env python3
"""
LLM交互日志查看器
用于查看和分析保存的LLM交互原始报文
"""

import json
import os
import argparse
from datetime import datetime
from typing import List, Dict, Any

def load_jsonl_file(file_path: str) -> List[Dict[Any, Any]]:
    """加载JSONL文件"""
    interactions = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        interaction = json.loads(line)
                        interactions.append(interaction)
                    except json.JSONDecodeError as e:
                        print(f"⚠️ 第{line_num}行JSON解析错误: {e}")
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
    
    return interactions

def load_summary_file(file_path: str) -> Dict[Any, Any]:
    """加载总结文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"⚠️ 总结文件不存在: {file_path}")
        return {}
    except Exception as e:
        print(f"❌ 读取总结文件失败: {e}")
        return {}

def display_interaction_summary(interactions: List[Dict[Any, Any]]):
    """显示交互概览"""
    print("📊 交互记录概览")
    print("=" * 80)
    
    if not interactions:
        print("❌ 没有找到交互记录")
        return
    
    print(f"📋 总交互次数: {len(interactions)}")
    
    # 统计交互类型
    type_counts = {}
    total_tokens = 0
    total_duration = 0
    
    for interaction in interactions:
        interaction_type = interaction.get('interaction_type', 'unknown')
        type_counts[interaction_type] = type_counts.get(interaction_type, 0) + 1
        total_tokens += interaction.get('total_tokens', 0)
        duration = interaction.get('duration_ms', 0)
        if duration:
            total_duration += duration
    
    print(f"🎯 总Token消耗: {total_tokens:,}")
    if total_duration > 0:
        print(f"⏱️ 总耗时: {total_duration:.1f}ms")
        print(f"📈 平均每次交互: {total_duration / len(interactions):.1f}ms")
    
    print(f"📋 交互类型分布:")
    for interaction_type, count in type_counts.items():
        print(f"   • {interaction_type}: {count}次")
    
    print()

def display_interaction_detail(interaction: Dict[Any, Any], show_raw: bool = False):
    """显示单个交互的详细信息"""
    print(f"🔄 交互 #{interaction.get('interaction_id', 'N/A')} [{interaction.get('timestamp', 'N/A')}]")
    print(f"📋 类型: {interaction.get('interaction_type', 'unknown')}")
    
    if interaction.get('function_name'):
        print(f"🔧 函数: {interaction['function_name']}")
        if interaction.get('function_args'):
            print(f"📝 参数: {json.dumps(interaction['function_args'], ensure_ascii=False)}")
    
    print(f"📊 Token统计: 输入={interaction.get('input_tokens', 0)}, 输出={interaction.get('output_tokens', 0)}, 总计={interaction.get('total_tokens', 0)}")
    
    if interaction.get('duration_ms'):
        print(f"⏱️ 耗时: {interaction['duration_ms']:.1f}ms")
    
    print("📤 输入内容:")
    input_content = interaction.get('input_content', '')
    if len(input_content) > 200:
        print(f"   {input_content[:200]}...")
    else:
        print(f"   {input_content}")
    
    print("📥 输出内容:")
    output_content = interaction.get('output_content', '')
    if len(output_content) > 200:
        print(f"   {output_content[:200]}...")
    else:
        print(f"   {output_content}")
    
    if show_raw:
        print("\n📡 原始API交互:")
        print("=" * 60)
        
        if interaction.get('raw_request'):
            print("📤 原始请求:")
            request_json = json.dumps(interaction['raw_request'], ensure_ascii=False, indent=2)
            if len(request_json) > 1000:
                print(f"{request_json[:1000]}...")
            else:
                print(request_json)
        
        if interaction.get('raw_response'):
            print("\n📥 原始响应:")
            response_json = json.dumps(interaction['raw_response'], ensure_ascii=False, indent=2)
            if len(response_json) > 1000:
                print(f"{response_json[:1000]}...")
            else:
                print(response_json)
    
    print("-" * 80)

def analyze_token_usage(interactions: List[Dict[Any, Any]]):
    """分析Token使用情况"""
    print("📈 Token使用分析")
    print("=" * 80)
    
    if not interactions:
        print("❌ 没有数据可分析")
        return
    
    total_input = sum(i.get('input_tokens', 0) for i in interactions)
    total_output = sum(i.get('output_tokens', 0) for i in interactions)
    total_tokens = total_input + total_output
    
    print(f"📊 Token统计:")
    print(f"   输入Token: {total_input:,}")
    print(f"   输出Token: {total_output:,}")
    print(f"   总计Token: {total_tokens:,}")
    
    if total_tokens > 0:
        input_ratio = (total_input / total_tokens) * 100
        output_ratio = (total_output / total_tokens) * 100
        print(f"   输入占比: {input_ratio:.1f}%")
        print(f"   输出占比: {output_ratio:.1f}%")
    
    # 按交互类型分析
    type_tokens = {}
    for interaction in interactions:
        interaction_type = interaction.get('interaction_type', 'unknown')
        if interaction_type not in type_tokens:
            type_tokens[interaction_type] = {'input': 0, 'output': 0, 'count': 0}
        
        type_tokens[interaction_type]['input'] += interaction.get('input_tokens', 0)
        type_tokens[interaction_type]['output'] += interaction.get('output_tokens', 0)
        type_tokens[interaction_type]['count'] += 1
    
    print(f"\n📋 按交互类型分析:")
    for interaction_type, stats in type_tokens.items():
        total = stats['input'] + stats['output']
        avg = total / stats['count'] if stats['count'] > 0 else 0
        print(f"   {interaction_type}:")
        print(f"     总Token: {total:,} (平均: {avg:.0f}/次)")
        print(f"     输入: {stats['input']:,}, 输出: {stats['output']:,}")
    
    print()

def analyze_performance(interactions: List[Dict[Any, Any]]):
    """分析性能数据"""
    print("⚡ 性能分析")
    print("=" * 80)
    
    durations = [i.get('duration_ms', 0) for i in interactions if i.get('duration_ms', 0) > 0]
    
    if not durations:
        print("❌ 没有性能数据可分析")
        return
    
    total_duration = sum(durations)
    avg_duration = total_duration / len(durations)
    min_duration = min(durations)
    max_duration = max(durations)
    
    print(f"⏱️ 响应时间统计:")
    print(f"   总耗时: {total_duration:.1f}ms")
    print(f"   平均耗时: {avg_duration:.1f}ms")
    print(f"   最快响应: {min_duration:.1f}ms")
    print(f"   最慢响应: {max_duration:.1f}ms")
    
    # 按交互类型分析性能
    type_performance = {}
    for interaction in interactions:
        duration = interaction.get('duration_ms', 0)
        if duration > 0:
            interaction_type = interaction.get('interaction_type', 'unknown')
            if interaction_type not in type_performance:
                type_performance[interaction_type] = []
            type_performance[interaction_type].append(duration)
    
    print(f"\n📋 按交互类型性能分析:")
    for interaction_type, durations in type_performance.items():
        avg = sum(durations) / len(durations)
        min_dur = min(durations)
        max_dur = max(durations)
        print(f"   {interaction_type}:")
        print(f"     平均: {avg:.1f}ms, 最快: {min_dur:.1f}ms, 最慢: {max_dur:.1f}ms")
    
    print()

def main():
    parser = argparse.ArgumentParser(description='LLM交互日志查看器')
    parser.add_argument('log_dir', help='日志目录路径')
    parser.add_argument('--detail', '-d', action='store_true', help='显示详细交互信息')
    parser.add_argument('--raw', '-r', action='store_true', help='显示原始API请求/响应')
    parser.add_argument('--analysis', '-a', action='store_true', help='显示详细分析')
    parser.add_argument('--interaction', '-i', type=int, help='显示指定交互的详细信息')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.log_dir):
        print(f"❌ 日志目录不存在: {args.log_dir}")
        return
    
    # 查找JSONL文件
    jsonl_files = [f for f in os.listdir(args.log_dir) if f.endswith('.jsonl')]
    
    if not jsonl_files:
        print(f"❌ 在目录 {args.log_dir} 中没有找到JSONL文件")
        return
    
    # 使用最新的JSONL文件
    latest_jsonl = sorted(jsonl_files)[-1]
    jsonl_path = os.path.join(args.log_dir, latest_jsonl)
    
    print(f"📁 读取日志文件: {jsonl_path}")
    interactions = load_jsonl_file(jsonl_path)
    
    if not interactions:
        print("❌ 没有找到有效的交互记录")
        return
    
    # 显示概览
    display_interaction_summary(interactions)
    
    # 显示指定交互的详细信息
    if args.interaction is not None:
        target_interaction = None
        for interaction in interactions:
            if interaction.get('interaction_id') == args.interaction:
                target_interaction = interaction
                break
        
        if target_interaction:
            print(f"🔍 交互 #{args.interaction} 详细信息:")
            print("=" * 80)
            display_interaction_detail(target_interaction, show_raw=True)
        else:
            print(f"❌ 没有找到交互 #{args.interaction}")
        return
    
    # 显示详细信息
    if args.detail:
        print("📋 详细交互记录:")
        print("=" * 80)
        for interaction in interactions:
            display_interaction_detail(interaction, show_raw=args.raw)
    
    # 显示分析
    if args.analysis:
        analyze_token_usage(interactions)
        analyze_performance(interactions)
    
    # 查找并显示总结文件
    summary_files = [f for f in os.listdir(args.log_dir) if f.endswith('_summary.json')]
    if summary_files:
        latest_summary = sorted(summary_files)[-1]
        summary_path = os.path.join(args.log_dir, latest_summary)
        summary = load_summary_file(summary_path)
        
        if summary:
            print("📊 会话总结:")
            print("=" * 80)
            print(json.dumps(summary, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
