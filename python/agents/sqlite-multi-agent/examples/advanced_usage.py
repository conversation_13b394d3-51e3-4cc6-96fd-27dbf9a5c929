#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Advanced usage example for SQLite Agent with custom database."""

import os
import sys
import sqlite3
import asyncio
from dotenv import load_dotenv

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from google.adk.runners import Runner
from sqlite_agent import sqlite_agent

# Load environment variables
load_dotenv()

def create_advanced_database(db_path: str):
    """Create a more complex database for advanced examples."""
    
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        
        # Create products table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT NOT NULL,
                price REAL NOT NULL,
                stock_quantity INTEGER NOT NULL,
                supplier_id INTEGER,
                created_date DATE DEFAULT CURRENT_DATE
            )
        ''')
        
        # Create orders table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT NOT NULL,
                order_date DATE NOT NULL,
                total_amount REAL NOT NULL,
                status TEXT DEFAULT 'pending'
            )
        ''')
        
        # Create order_items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                FOREIGN KEY (order_id) REFERENCES orders (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # Create suppliers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_email TEXT,
                phone TEXT,
                address TEXT
            )
        ''')
        
        # Insert sample data
        suppliers_data = [
            ('TechCorp Supplies', '<EMAIL>', '555-0101', '123 Tech St'),
            ('Office Plus', '<EMAIL>', '555-0102', '456 Business Ave'),
            ('Global Electronics', '<EMAIL>', '555-0103', '789 Circuit Rd')
        ]
        cursor.executemany(
            'INSERT OR IGNORE INTO suppliers (name, contact_email, phone, address) VALUES (?, ?, ?, ?)',
            suppliers_data
        )
        
        products_data = [
            ('Laptop Pro 15"', 'Electronics', 1299.99, 25, 1, '2024-01-15'),
            ('Wireless Mouse', 'Electronics', 29.99, 150, 1, '2024-01-20'),
            ('Office Chair', 'Furniture', 199.99, 45, 2, '2024-02-01'),
            ('Desk Lamp', 'Furniture', 49.99, 80, 2, '2024-02-05'),
            ('Smartphone X', 'Electronics', 799.99, 60, 3, '2024-02-10'),
            ('Tablet Mini', 'Electronics', 399.99, 35, 3, '2024-02-15')
        ]
        cursor.executemany(
            'INSERT OR IGNORE INTO products (name, category, price, stock_quantity, supplier_id, created_date) VALUES (?, ?, ?, ?, ?, ?)',
            products_data
        )
        
        orders_data = [
            ('John Smith', '2024-03-01', 1329.98, 'completed'),
            ('Jane Doe', '2024-03-02', 249.98, 'completed'),
            ('Bob Johnson', '2024-03-03', 829.98, 'pending'),
            ('Alice Brown', '2024-03-04', 449.98, 'shipped')
        ]
        cursor.executemany(
            'INSERT OR IGNORE INTO orders (customer_name, order_date, total_amount, status) VALUES (?, ?, ?, ?)',
            orders_data
        )
        
        order_items_data = [
            (1, 1, 1, 1299.99),  # John: Laptop
            (1, 2, 1, 29.99),    # John: Mouse
            (2, 3, 1, 199.99),   # Jane: Chair
            (2, 4, 1, 49.99),    # Jane: Lamp
            (3, 5, 1, 799.99),   # Bob: Smartphone
            (3, 2, 1, 29.99),    # Bob: Mouse
            (4, 6, 1, 399.99),   # Alice: Tablet
            (4, 4, 1, 49.99)     # Alice: Lamp
        ]
        cursor.executemany(
            'INSERT OR IGNORE INTO order_items (order_id, product_id, quantity, unit_price) VALUES (?, ?, ?, ?)',
            order_items_data
        )
        
        conn.commit()
        print(f"✅ Advanced database created at: {db_path}")


async def run_advanced_examples():
    """Run advanced examples with complex queries."""
    
    # Create advanced database
    advanced_db_path = "data/advanced_sample.db"
    create_advanced_database(advanced_db_path)
    
    # Set database path for this session
    os.environ["SQLITE_DATABASE_PATH"] = advanced_db_path
    
    # Create runner
    from google.adk.runners import InMemoryRunner

    runner = InMemoryRunner(
        app_name="sqlite_agent_advanced",
        agent=sqlite_agent
    )
    
    session = await runner.session_service.create_session(
        app_name="sqlite_agent_advanced",
        user_id="advanced_user"
    )
    
    # Advanced example queries
    advanced_queries = [
        "What is the database schema? Show me all tables and their relationships.",
        "What are the top 3 best-selling products by total revenue?",
        "Show me the monthly sales trend for the last 3 months.",
        "Which suppliers provide the most expensive products on average?",
        "Find customers who have placed orders worth more than $500.",
        "What is the inventory value by category?",
        "Show me products that are running low on stock (less than 50 units).",
        "Calculate the profit margin if we assume 30% cost of goods sold.",
    ]
    
    print("🚀 Advanced SQLite Agent Examples")
    print("=" * 60)
    print(f"Using database: {advanced_db_path}")
    print("=" * 60)
    
    for i, query in enumerate(advanced_queries, 1):
        print(f"\n📊 Example {i}: {query}")
        print("-" * 50)
        
        try:
            # Create message content
            from google.genai import types
            content = types.Content(
                role="user",
                parts=[types.Part(text=query)]
            )

            response_parts = []
            async for event in runner.run_async(
                user_id="advanced_user",
                session_id=session.id,
                new_message=content
            ):
                if event.content and event.content.parts:
                    for part in event.content.parts:
                        if part.text:
                            response_parts.append(part.text)
                            print(part.text, end='', flush=True)
            
            print("\n")
            
            # Wait a bit between queries
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ Error processing query: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎉 Advanced examples completed!")
    print("=" * 60)
    
    print("Session completed.")


async def main():
    """Main function for advanced usage."""

    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ Please set DEEPSEEK_API_KEY environment variable")
        return
    
    try:
        await run_advanced_examples()
    except KeyboardInterrupt:
        print("\n👋 Examples interrupted.")
    except Exception as e:
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
