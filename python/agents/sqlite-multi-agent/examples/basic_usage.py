#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Basic usage example for SQLite Agent."""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from google.adk.runners import InMemoryRunner
from sqlite_agent import sqlite_agent

# Load environment variables
load_dotenv()

async def main():
    """Main function demonstrating basic usage."""

    # Check required environment variables
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("Please set DEEPSEEK_API_KEY environment variable")
        return

    if not os.getenv("SQLITE_DATABASE_PATH"):
        print("Please set SQLITE_DATABASE_PATH environment variable")
        return
    
    # Create runner
    from google.adk.runners import InMemoryRunner

    runner = InMemoryRunner(
        app_name="sqlite_agent_demo",
        agent=sqlite_agent
    )
    
    print("🚀 Starting SQLite Agent Demo")
    print("=" * 50)
    
    # Example queries to demonstrate capabilities
    example_queries = [
        "What tables are available in the database?",
        "Show me information about the employees table",
        "Get some sample data from the employees table",
        "How many employees are there in total?",
        "Who are the highest paid employees?",
        "What is the average salary by department?",
        "Show me all employees hired after 2022",
    ]
    
    print("Example queries you can try:")
    for i, query in enumerate(example_queries, 1):
        print(f"{i}. {query}")
    
    print("\n" + "=" * 50)
    print("Starting interactive session...")
    print("Type 'quit' or 'exit' to end the session")
    print("=" * 50)
    
    # Start interactive session
    session = await runner.session_service.create_session(
        app_name="sqlite_agent_demo",
        user_id="demo_user"
    )

    try:
        while True:
            user_input = input("\n💬 You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            if not user_input:
                continue

            print("🤖 SQLite Agent:")

            # Create message content
            from google.genai import types
            content = types.Content(
                role="user",
                parts=[types.Part(text=user_input)]
            )

            # Stream the response
            async for event in runner.run_async(
                user_id="demo_user",
                session_id=session.id,
                new_message=content
            ):
                if event.content and event.content.parts:
                    for part in event.content.parts:
                        if part.text:
                            print(part.text, end='', flush=True)

            print()  # New line after response
            
    except KeyboardInterrupt:
        print("\n👋 Session interrupted. Goodbye!")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    finally:
        # Clean up
        print("Session ended.")


if __name__ == "__main__":
    asyncio.run(main())
