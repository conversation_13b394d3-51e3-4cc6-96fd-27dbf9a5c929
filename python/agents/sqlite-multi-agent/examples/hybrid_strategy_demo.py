#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
混合策略SQLite Agent演示

展示如何使用智能工具选择、专门子Agent和上下文感知功能。
"""

import asyncio
import os
from dotenv import load_dotenv

from google.adk.runners import InMemoryRunner
from google.genai import types

from sqlite_agent import sqlite_agent

# 加载环境变量
load_dotenv()


async def demo_hybrid_strategy():
    """演示混合策略的工具选择。"""
    
    print("🚀 SQLite Agent 混合策略演示")
    print("=" * 50)
    
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ 请设置 DEEPSEEK_API_KEY 环境变量")
        return
    
    if not os.getenv("SQLITE_DATABASE_PATH"):
        print("❌ 请设置 SQLITE_DATABASE_PATH 环境变量")
        return
    
    # 创建Runner
    runner = InMemoryRunner(
        app_name="sqlite_agent_hybrid",
        agent=sqlite_agent
    )
    
    # 创建会话
    session = await runner.session_service.create_session(
        app_name="sqlite_agent_hybrid",
        user_id="demo_user"
    )
    
    # 测试场景
    test_scenarios = [
        {
            "name": "数据库探索",
            "query": "这个数据库有哪些表？",
            "expected_mode": "exploration"
        },
        {
            "name": "表结构查询", 
            "query": "users表的结构是什么？",
            "expected_mode": "exploration"
        },
        {
            "name": "数据查询",
            "query": "查询所有用户的姓名和邮箱",
            "expected_mode": "query"
        },
        {
            "name": "数据分析",
            "query": "分析订单表中的销售趋势",
            "expected_mode": "analysis"
        },
        {
            "name": "复杂分析",
            "query": "统计每个月的订单数量和总金额",
            "expected_mode": "analysis"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 场景 {i}: {scenario['name']}")
        print(f"🔍 查询: {scenario['query']}")
        print(f"🎯 预期模式: {scenario['expected_mode']}")
        print("-" * 40)
        
        # 创建消息内容
        content = types.Content(
            role="user",
            parts=[types.Part(text=scenario['query'])]
        )
        
        # 执行查询
        try:
            print("🤖 SQLite Agent:")
            async for event in runner.run_async(
                user_id="demo_user",
                session_id=session.id,
                new_message=content
            ):
                if event.content and event.content.parts:
                    for part in event.content.parts:
                        if part.text:
                            print(part.text, end='', flush=True)
            print()  # 换行
            
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
        
        print("\n" + "=" * 50)
        
        # 等待用户确认继续
        if i < len(test_scenarios):
            input("按回车键继续下一个场景...")


async def demo_tool_selection():
    """演示智能工具选择机制。"""
    
    print("\n🧠 智能工具选择演示")
    print("=" * 50)
    
    from sqlite_agent.smart_toolset import SQLiteSmartToolset
    
    # 创建智能工具集
    smart_toolset = SQLiteSmartToolset()
    
    # 测试不同类型的查询
    test_queries = [
        "显示所有表",
        "users表的结构",
        "查询用户数据",
        "分析销售趋势",
        "统计订单信息"
    ]
    
    for query in test_queries:
        print(f"\n🔍 查询: {query}")
        
        # 模拟上下文
        class MockContext:
            def __init__(self, query):
                self.user_query = query
        
        context = MockContext(query)
        
        # 获取相关工具
        tools = await smart_toolset.get_tools(context)
        
        print(f"🛠️  选择的工具: {[tool.name for tool in tools]}")
        
        # 分析意图
        intent = smart_toolset._analyze_intent(query.lower())
        print(f"🎯 识别意图: {intent}")


async def main():
    """主函数。"""
    
    print("🎉 SQLite Agent 混合策略完整演示")
    print("=" * 60)
    
    # 演示混合策略
    await demo_hybrid_strategy()
    
    # 演示工具选择
    await demo_tool_selection()
    
    print("\n✅ 演示完成！")
    print("\n📝 混合策略特点：")
    print("1. 🎯 智能工具选择 - 根据用户意图自动选择最相关的工具")
    print("2. 🤖 专门子Agent - 复杂任务交给专门的Agent处理")
    print("3. 🧠 上下文感知 - 根据会话历史动态调整工具选择")
    print("4. 🔄 向后兼容 - 保持原有基础工具的可用性")
    print("5. 🚀 性能优化 - 减少不必要的工具暴露，提高响应速度")


if __name__ == "__main__":
    asyncio.run(main())
