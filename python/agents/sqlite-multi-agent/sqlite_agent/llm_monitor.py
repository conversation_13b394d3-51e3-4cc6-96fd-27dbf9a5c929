# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""LLM交互监控器 - 监控每次LLM调用的详细信息和Token消耗"""

import json
import time
import os
from typing import Dict, List, Any, Optional
import tiktoken
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class LLMInteraction:
    """单次LLM交互记录"""
    interaction_id: int
    timestamp: str
    interaction_type: str  # "function_call", "function_response", "final_answer"
    input_tokens: int
    output_tokens: int
    total_tokens: int
    input_content: str
    output_content: str
    function_name: Optional[str] = None
    function_args: Optional[Dict] = None
    duration_ms: Optional[float] = None
    raw_request: Optional[Dict] = None  # 原始API请求
    raw_response: Optional[Dict] = None  # 原始API响应
    token_source: Optional[str] = None  # token来源："api"表示真实API返回，"estimated"表示本地估算


@dataclass
class SessionStats:
    """会话统计信息"""
    total_interactions: int = 0
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    total_tokens: int = 0
    total_duration_ms: float = 0
    interactions: List[LLMInteraction] = field(default_factory=list)


class LLMMonitor:
    """LLM交互监控器"""

    def __init__(self, enable_detailed_logging: bool = True, max_content_length: int = 1000, show_raw_api: bool = False, save_to_file: bool = True, output_dir: str = "llm_logs"):
        self.enable_detailed_logging = enable_detailed_logging
        self.max_content_length = max_content_length  # 可配置的内容长度限制
        self.show_raw_api = show_raw_api  # 是否显示原始API请求/响应
        self.save_to_file = save_to_file
        self.output_dir = output_dir
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        self.session_stats = SessionStats()
        self.current_interaction_id = 0

        # 创建输出目录和日志文件
        if self.save_to_file:
            os.makedirs(self.output_dir, exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.log_file = os.path.join(self.output_dir, f"llm_interactions_{timestamp}.jsonl")
            print(f"📁 LLM交互日志将保存到: {self.log_file}")

        # 打印监控启动信息
        if self.enable_detailed_logging:
            print("🔍 LLM交互监控器已启动")
            if self.show_raw_api:
                print("📡 原始API请求/响应监控已启用")
            print("=" * 80)
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        if not text:
            return 0
        return len(self.tokenizer.encode(str(text)))

    def _save_interaction_to_file(self, interaction: LLMInteraction):
        """将交互记录保存到文件"""
        if not self.save_to_file:
            return

        try:
            # 将交互记录转换为字典
            interaction_dict = {
                "interaction_id": interaction.interaction_id,
                "timestamp": interaction.timestamp,
                "interaction_type": interaction.interaction_type,
                "input_content": interaction.input_content,
                "output_content": interaction.output_content,
                "function_name": interaction.function_name,
                "function_args": interaction.function_args,
                "input_tokens": interaction.input_tokens,
                "output_tokens": interaction.output_tokens,
                "total_tokens": interaction.total_tokens,
                "token_source": getattr(interaction, 'token_source', 'unknown'),  # 添加token来源信息
                "duration_ms": interaction.duration_ms,
                "raw_request": interaction.raw_request,
                "raw_response": interaction.raw_response
            }

            # 以JSONL格式追加到文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(interaction_dict, ensure_ascii=False) + '\n')

        except Exception as e:
            print(f"⚠️ 保存交互记录到文件失败: {e}")
    
    def log_interaction(
        self,
        interaction_type: str,
        input_content: str,
        output_content: str,
        function_name: Optional[str] = None,
        function_args: Optional[Dict] = None,
        duration_ms: Optional[float] = None,
        raw_request: Optional[Dict] = None,
        raw_response: Optional[Dict] = None,
        usage_metadata: Optional[Any] = None  # 新增参数用于传递真实的usage信息
    ) -> LLMInteraction:
        """记录一次LLM交互"""

        self.current_interaction_id += 1

        # 只使用真实的LLM API返回的token使用量，不进行本地估算
        if usage_metadata:
            input_tokens = getattr(usage_metadata, 'prompt_token_count', 0)
            output_tokens = getattr(usage_metadata, 'candidates_token_count', 0)
            total_tokens = getattr(usage_metadata, 'total_token_count', input_tokens + output_tokens)
            token_source = "api"
        elif raw_response and isinstance(raw_response, dict) and 'usage' in raw_response:
            # 从raw_response中提取usage信息（备用方案）
            usage = raw_response['usage']
            input_tokens = usage.get('prompt_tokens', 0)
            output_tokens = usage.get('completion_tokens', 0)
            total_tokens = usage.get('total_tokens', input_tokens + output_tokens)
            token_source = "api"
        else:
            # 没有真实token数据时，不记录token信息
            input_tokens = 0
            output_tokens = 0
            total_tokens = 0
            token_source = "unavailable"
        
        # 创建交互记录
        interaction = LLMInteraction(
            interaction_id=self.current_interaction_id,
            timestamp=datetime.now().strftime("%H:%M:%S.%f")[:-3],
            interaction_type=interaction_type,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens,
            input_content=input_content,
            output_content=output_content,
            function_name=function_name,
            function_args=function_args,
            duration_ms=duration_ms,
            raw_request=raw_request,
            raw_response=raw_response
        )

        # 设置token来源标识
        interaction.token_source = token_source
        
        # 更新会话统计
        self.session_stats.total_interactions += 1
        # 只统计有真实token数据的交互
        if token_source == "api":
            self.session_stats.total_input_tokens += input_tokens
            self.session_stats.total_output_tokens += output_tokens
            self.session_stats.total_tokens += total_tokens
        if duration_ms:
            self.session_stats.total_duration_ms += duration_ms
        self.session_stats.interactions.append(interaction)

        # 保存到文件
        self._save_interaction_to_file(interaction)

        # 打印详细日志
        if self.enable_detailed_logging:
            self._print_interaction(interaction)

        return interaction
    
    def _print_interaction(self, interaction: LLMInteraction):
        """打印交互详情"""
        
        print(f"\n🔄 第{interaction.interaction_id}次LLM交互 [{interaction.timestamp}]")
        print(f"📋 类型: {self._get_interaction_type_display(interaction.interaction_type)}")
        
        if interaction.function_name:
            print(f"🔧 函数: {interaction.function_name}")
            if interaction.function_args:
                args_str = json.dumps(interaction.function_args, ensure_ascii=False, indent=2)
                print(f"📝 参数: {args_str}")
        
        print("-" * 60)
        
        # 输入信息
        print(f"📤 输入 ({interaction.input_tokens} tokens):")
        input_preview = self._truncate_content(interaction.input_content, 300)
        print(f"   {input_preview}")

        # 输出信息
        print(f"📥 输出 ({interaction.output_tokens} tokens):")
        output_preview = self._truncate_content(interaction.output_content, self.max_content_length)
        print(f"   {output_preview}")

        # 如果输出被截断，提供提示
        if len(interaction.output_content) > self.max_content_length:
            print(f"   📝 注意: 输出内容已截断 (完整长度: {len(interaction.output_content)} 字符)")
            print(f"   💡 提示: 可以增加 max_content_length 参数查看完整内容")

        # 显示原始API请求/响应
        if self.show_raw_api:
            self._print_raw_api_data(interaction)

        # Token统计
        if hasattr(interaction, 'token_source') and interaction.token_source == "api":
            print(f"📊 Token消耗: 输入{interaction.input_tokens} + 输出{interaction.output_tokens} = 总计{interaction.total_tokens} ✅ (真实API返回)")
        elif hasattr(interaction, 'token_source') and interaction.token_source == "unavailable":
            print(f"📊 Token消耗: 无法获取 ⚠️ (API未返回token信息)")
        else:
            # 兼容旧数据
            print(f"📊 Token消耗: 输入{interaction.input_tokens} + 输出{interaction.output_tokens} = 总计{interaction.total_tokens}")

        if interaction.duration_ms:
            print(f"⏱️  耗时: {interaction.duration_ms:.1f}ms")

        print("=" * 80)
    
    def _get_interaction_type_display(self, interaction_type: str) -> str:
        """获取交互类型的显示名称"""
        type_map = {
            "function_call": "🔧 函数调用",
            "function_response": "📊 函数响应处理", 
            "final_answer": "💬 最终回答生成",
            "tool_selection": "🛠️ 工具选择",
            "result_processing": "⚙️ 结果处理"
        }
        return type_map.get(interaction_type, interaction_type)
    
    def _truncate_content(self, content: str, max_length: int) -> str:
        """截断内容用于显示"""
        if len(content) <= max_length:
            return content
        return content[:max_length] + "..."

    def _print_raw_api_data(self, interaction: LLMInteraction):
        """打印原始API请求和响应数据"""
        print("\n📡 原始API交互:")
        print("-" * 40)

        # 显示原始请求
        if interaction.raw_request:
            print("📤 完整原始请求 (发送给DeepSeek):")
            request_json = json.dumps(interaction.raw_request, ensure_ascii=False, indent=2)
            print(f"```json\n{request_json}\n```")
            print(f"📏 请求大小: {len(request_json)} 字符")
            print()

        # 显示原始响应
        if interaction.raw_response:
            print("📥 完整原始响应 (DeepSeek返回):")
            response_json = json.dumps(interaction.raw_response, ensure_ascii=False, indent=2)
            print(f"```json\n{response_json}\n```")
            print(f"📏 响应大小: {len(response_json)} 字符")
            print()
    
    def print_session_summary(self):
        """打印会话总结"""
        stats = self.session_stats
        
        # 统计有token数据的交互
        token_interactions = [i for i in stats.interactions if hasattr(i, 'token_source') and i.token_source == "api"]
        no_token_interactions = [i for i in stats.interactions if not hasattr(i, 'token_source') or i.token_source == "unavailable"]

        print("\n📊 会话总结")
        print("=" * 80)
        print(f"🔄 总交互次数: {stats.total_interactions}")
        print(f"   ✅ 有Token数据: {len(token_interactions)}")
        print(f"   ⚠️  无Token数据: {len(no_token_interactions)}")
        print(f"📤 总输入tokens: {stats.total_input_tokens:,} (仅统计真实API返回)")
        print(f"📥 总输出tokens: {stats.total_output_tokens:,} (仅统计真实API返回)")
        print(f"📊 总计tokens: {stats.total_tokens:,} (仅统计真实API返回)")
        
        if stats.total_duration_ms > 0:
            print(f"⏱️  总耗时: {stats.total_duration_ms:.1f}ms")
            print(f"📈 平均每次交互: {stats.total_duration_ms / stats.total_interactions:.1f}ms")
        
        # 只基于有token数据的交互计算平均值
        if len(token_interactions) > 0:
            avg_input = stats.total_input_tokens / len(token_interactions)
            avg_output = stats.total_output_tokens / len(token_interactions)
            avg_total = stats.total_tokens / len(token_interactions)

            print(f"📈 平均每次有Token数据的交互:")
            print(f"   输入: {avg_input:.0f} tokens")
            print(f"   输出: {avg_output:.0f} tokens")
            print(f"   总计: {avg_total:.0f} tokens")
        elif stats.total_interactions > 0:
            print(f"⚠️  无法计算平均Token消耗 (所有交互都没有Token数据)")
        
        # Token分布分析
        input_ratio = (stats.total_input_tokens / stats.total_tokens) * 100 if stats.total_tokens > 0 else 0
        output_ratio = (stats.total_output_tokens / stats.total_tokens) * 100 if stats.total_tokens > 0 else 0
        
        print(f"📊 Token分布:")
        print(f"   输入占比: {input_ratio:.1f}%")
        print(f"   输出占比: {output_ratio:.1f}%")
        
        # 交互类型统计
        type_counts = {}
        for interaction in stats.interactions:
            type_counts[interaction.interaction_type] = type_counts.get(interaction.interaction_type, 0) + 1
        
        if type_counts:
            print(f"📋 交互类型分布:")
            for interaction_type, count in type_counts.items():
                display_name = self._get_interaction_type_display(interaction_type)
                print(f"   {display_name}: {count}次")
        
        print("=" * 80)

    def save_session_summary(self):
        """保存会话总结到文件"""
        if not self.save_to_file:
            print("⚠️ 文件保存功能未启用")
            return

        try:
            stats = self.session_stats

            # 创建总结数据
            summary = {
                "session_summary": {
                    "timestamp": datetime.now().isoformat(),
                    "total_interactions": stats.total_interactions,
                    "total_input_tokens": stats.total_input_tokens,
                    "total_output_tokens": stats.total_output_tokens,
                    "total_tokens": stats.total_tokens,
                    "total_duration_ms": stats.total_duration_ms,
                    "average_duration_per_interaction": stats.total_duration_ms / stats.total_interactions if stats.total_interactions > 0 else 0,
                    "average_input_tokens": stats.total_input_tokens / stats.total_interactions if stats.total_interactions > 0 else 0,
                    "average_output_tokens": stats.total_output_tokens / stats.total_interactions if stats.total_interactions > 0 else 0,
                    "input_token_ratio": (stats.total_input_tokens / stats.total_tokens) * 100 if stats.total_tokens > 0 else 0,
                    "output_token_ratio": (stats.total_output_tokens / stats.total_tokens) * 100 if stats.total_tokens > 0 else 0
                }
            }

            # 交互类型统计
            type_counts = {}
            for interaction in stats.interactions:
                type_counts[interaction.interaction_type] = type_counts.get(interaction.interaction_type, 0) + 1
            summary["session_summary"]["interaction_types"] = type_counts

            # 保存总结到文件
            summary_file = self.log_file.replace('.jsonl', '_summary.json')
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            print(f"📁 会话总结已保存到: {summary_file}")

        except Exception as e:
            print(f"⚠️ 保存会话总结失败: {e}")

    def get_stats(self) -> SessionStats:
        """获取会话统计信息"""
        return self.session_stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.session_stats = SessionStats()
        self.current_interaction_id = 0
        
        if self.enable_detailed_logging:
            print("🔄 监控器统计信息已重置")


# 全局监控器实例
_global_monitor: Optional[LLMMonitor] = None


def get_monitor() -> LLMMonitor:
    """获取全局监控器实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = LLMMonitor()
    return _global_monitor


def enable_monitoring(detailed_logging: bool = True, max_content_length: int = 1000, show_raw_api: bool = False, save_to_file: bool = True, output_dir: str = "llm_logs"):
    """启用LLM监控"""
    global _global_monitor
    _global_monitor = LLMMonitor(
        enable_detailed_logging=detailed_logging,
        max_content_length=max_content_length,
        show_raw_api=show_raw_api,
        save_to_file=save_to_file,
        output_dir=output_dir
    )


def disable_monitoring():
    """禁用LLM监控"""
    global _global_monitor
    _global_monitor = None


def log_llm_interaction(
    interaction_type: str,
    input_content: str,
    output_content: str,
    function_name: Optional[str] = None,
    function_args: Optional[Dict] = None,
    duration_ms: Optional[float] = None,
    raw_request: Optional[Dict] = None,
    raw_response: Optional[Dict] = None,
    usage_metadata: Optional[Any] = None
) -> Optional[LLMInteraction]:
    """记录LLM交互（便捷函数）"""
    monitor = get_monitor()
    if monitor:
        return monitor.log_interaction(
            interaction_type=interaction_type,
            input_content=input_content,
            output_content=output_content,
            function_name=function_name,
            function_args=function_args,
            duration_ms=duration_ms,
            raw_request=raw_request,
            raw_response=raw_response,
            usage_metadata=usage_metadata
        )
    return None


def print_session_summary():
    """打印会话总结（便捷函数）"""
    monitor = get_monitor()
    if monitor:
        monitor.print_session_summary()


def save_session_summary():
    """保存会话总结到文件（便捷函数）"""
    monitor = get_monitor()
    if monitor:
        monitor.save_session_summary()
