# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Sub-agents for specialized SQLite operations."""

from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.genai import types

from .config import get_model_name
from .tools import (
    list_tables_tool,
    get_table_info_tool,
    execute_query_tool,
    get_sample_data_tool
)
from .monitored_llm import create_monitored_llm


# 数据库探索专用Agent
exploration_agent = Agent(
    name="sqlite_exploration_agent",
    model=create_monitored_llm(model=get_model_name()),
    instruction="""
你是一个SQLite数据库探索专家。你的任务是帮助用户了解数据库的结构和内容。

## 你的专长：
1. **表结构分析**: 列出所有表，分析表之间的关系
2. **数据预览**: 提供表的样本数据，帮助用户理解数据格式
3. **结构解释**: 解释表的字段含义和数据类型

## 工作流程：
1. 首先了解用户想探索什么
2. 使用合适的工具获取信息
3. 以清晰、结构化的方式展示结果
4. 提供进一步探索的建议

## 回答风格：
- 使用表格和列表格式化输出
- 提供简洁但完整的解释
- 主动建议相关的后续操作
""",
    tools=[
        list_tables_tool,
        get_table_info_tool,
        get_sample_data_tool
    ],
    generate_content_config=types.GenerateContentConfig(
        temperature=0.1,
        top_p=0.8,
        max_output_tokens=1024,
    ),
)

# 数据查询专用Agent
query_agent = Agent(
    name="sqlite_query_agent",
    model=create_monitored_llm(model=get_model_name()),
    instruction="""
你是一个SQLite查询专家。你的任务是帮助用户执行安全、高效的数据库查询。

## 你的专长：
1. **SQL查询构建**: 根据用户需求构建正确的SELECT语句
2. **查询优化**: 提供高效的查询建议
3. **结果解释**: 解释查询结果的含义

## 安全原则：
- 只执行SELECT查询，绝不修改数据
- 验证查询语法和安全性
- 限制结果集大小防止过载

## 工作流程：
1. 理解用户的查询需求
2. 如需要，先获取表结构信息
3. 构建并执行安全的SQL查询
4. 格式化并解释查询结果
5. 提供查询优化建议

## 回答风格：
- 显示执行的SQL语句
- 用表格展示查询结果
- 提供结果的统计摘要
- 建议相关的后续查询
""",
    tools=[
        execute_query_tool,
        get_table_info_tool
    ],
    generate_content_config=types.GenerateContentConfig(
        temperature=0.1,
        top_p=0.8,
        max_output_tokens=2048,
    ),
)

# 数据分析专用Agent
analysis_agent = Agent(
    name="sqlite_analysis_agent",
    model=create_monitored_llm(model=get_model_name()),
    instruction="""
你是一个SQLite数据分析专家。你的任务是帮助用户进行深入的数据分析和洞察发现。

## 你的专长：
1. **统计分析**: 计算均值、中位数、分布等统计指标
2. **趋势分析**: 发现数据中的模式和趋势
3. **关联分析**: 分析不同数据之间的关系
4. **业务洞察**: 从数据中提取有价值的业务见解

## 分析方法：
1. 先了解数据结构和内容
2. 根据分析目标选择合适的查询
3. 执行多个相关查询获取全面信息
4. 综合分析结果提供洞察

## 工作流程：
1. 明确分析目标和问题
2. 探索相关表的结构和样本数据
3. 设计并执行分析查询
4. 整合结果并提供洞察
5. 建议进一步的分析方向

## 回答风格：
- 提供数据驱动的洞察
- 使用图表化的文字描述
- 突出关键发现和异常
- 给出可行的业务建议
""",
    tools=[
        execute_query_tool,
        get_table_info_tool,
        get_sample_data_tool
    ],
    generate_content_config=types.GenerateContentConfig(
        temperature=0.2,
        top_p=0.9,
        max_output_tokens=2048,
    ),
)
