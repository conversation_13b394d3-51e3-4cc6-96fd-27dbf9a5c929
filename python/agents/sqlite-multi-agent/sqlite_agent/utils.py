# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Utility functions for SQLite Agent."""

import sqlite3
import os
import re
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
from tabulate import tabulate

from .config import get_sqlite_config


@contextmanager
def get_database_connection(database_path: str):
    """Get a database connection with proper error handling."""
    config = get_sqlite_config()
    
    if not os.path.exists(database_path):
        raise FileNotFoundError(f"Database file not found: {database_path}")
    
    conn = None
    try:
        # Open in read-only mode if configured
        if config.read_only:
            conn = sqlite3.connect(f"file:{database_path}?mode=ro", uri=True, timeout=config.timeout)
        else:
            conn = sqlite3.connect(database_path, timeout=config.timeout)
        
        conn.row_factory = sqlite3.Row  # Enable column access by name
        yield conn
        
    except Exception as e:
        if conn:
            conn.rollback()
        raise e
    finally:
        if conn:
            conn.close()


def validate_query(query: str) -> Dict[str, Any]:
    """Validate SQL query for safety and correctness."""
    if not query or not query.strip():
        return {"valid": False, "error": "Query cannot be empty"}
    
    query_upper = query.upper().strip()
    
    # Check if it's a SELECT query
    if not query_upper.startswith('SELECT'):
        return {"valid": False, "error": "Only SELECT queries are allowed"}
    
    # Check for dangerous keywords
    dangerous_keywords = [
        'INSERT', 'UPDATE', 'DELETE', 'DROP', 'ALTER', 'CREATE', 
        'TRUNCATE', 'REPLACE', 'ATTACH', 'DETACH', 'PRAGMA'
    ]
    
    for keyword in dangerous_keywords:
        if re.search(rf'\b{keyword}\b', query_upper):
            return {"valid": False, "error": f"Keyword '{keyword}' is not allowed"}
    
    # Check for comment-based SQL injection attempts
    if '--' in query or '/*' in query or '*/' in query:
        return {"valid": False, "error": "Comments are not allowed in queries"}
    
    # Check for semicolon (multiple statements)
    if ';' in query.rstrip(';'):
        return {"valid": False, "error": "Multiple statements are not allowed"}
    
    return {"valid": True, "error": None}


def format_results(results: List[Any], columns: List[str], max_results: int) -> str:
    """Format query results for display."""
    if not results:
        return "No results found."
    
    # Convert results to list of lists for tabulate
    formatted_rows = []
    for row in results:
        if hasattr(row, '_fields'):  # sqlite3.Row object
            formatted_rows.append([str(value) if value is not None else 'NULL' for value in row])
        else:
            formatted_rows.append([str(value) if value is not None else 'NULL' for value in row])
    
    # Create table
    table = tabulate(formatted_rows, headers=columns, tablefmt="grid")
    
    result = f"Query Results ({len(results)} rows):\n\n{table}"
    
    # Add warning if results were limited
    if len(results) >= max_results:
        result += f"\n\n⚠️ Results limited to {max_results} rows. Use LIMIT clause for more control."
    
    return result


def sanitize_table_name(table_name: str) -> str:
    """Sanitize table name to prevent injection."""
    # Remove any non-alphanumeric characters except underscores
    sanitized = re.sub(r'[^a-zA-Z0-9_]', '', table_name)
    
    # Ensure it doesn't start with a number
    if sanitized and sanitized[0].isdigit():
        sanitized = f"table_{sanitized}"
    
    return sanitized


def create_sample_database(database_path: str) -> None:
    """Create a sample SQLite database for testing."""
    os.makedirs(os.path.dirname(database_path), exist_ok=True)
    
    with sqlite3.connect(database_path) as conn:
        cursor = conn.cursor()
        
        # Create employees table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                department TEXT NOT NULL,
                salary REAL NOT NULL,
                hire_date DATE NOT NULL
            )
        ''')
        
        # Create departments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS departments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                manager TEXT,
                budget REAL
            )
        ''')
        
        # Insert sample data
        employees_data = [
            ('Alice Johnson', 'Engineering', 75000, '2022-01-15'),
            ('Bob Smith', 'Marketing', 65000, '2021-03-20'),
            ('Carol Davis', 'Engineering', 80000, '2020-07-10'),
            ('David Wilson', 'Sales', 70000, '2023-02-01'),
            ('Eve Brown', 'HR', 60000, '2021-11-30')
        ]
        
        cursor.executemany(
            'INSERT OR IGNORE INTO employees (name, department, salary, hire_date) VALUES (?, ?, ?, ?)',
            employees_data
        )
        
        departments_data = [
            ('Engineering', 'Alice Johnson', 500000),
            ('Marketing', 'Bob Smith', 200000),
            ('Sales', 'David Wilson', 300000),
            ('HR', 'Eve Brown', 150000)
        ]
        
        cursor.executemany(
            'INSERT OR IGNORE INTO departments (name, manager, budget) VALUES (?, ?, ?)',
            departments_data
        )
        
        conn.commit()


def get_query_suggestions(table_name: str) -> List[str]:
    """Get suggested queries for a table."""
    suggestions = [
        f"SELECT * FROM {table_name} LIMIT 10;",
        f"SELECT COUNT(*) FROM {table_name};",
        f"SELECT DISTINCT column_name FROM {table_name};",  # Replace column_name with actual column
        f"SELECT * FROM {table_name} ORDER BY column_name DESC LIMIT 5;"  # Replace column_name
    ]
    return suggestions
