# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""SQLite Agent implementation using Google ADK."""

import os
from typing import Optional

from google.adk.agents import Agent
from google.adk.agents.callback_context import CallbackContext
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.agent_tool import AgentTool
from google.genai import types

from .config import get_sqlite_config, get_model_name, get_deepseek_api_key
from .prompts import get_sqlite_agent_instructions
from .utils import create_sample_database, get_database_connection
from .smart_toolset import sqlite_smart_toolset
from .sub_agents import exploration_agent, query_agent, analysis_agent
from .llm_monitor import enable_monitoring, print_session_summary, save_session_summary
from .monitored_llm import create_monitored_llm

# 配置选项：是否启用子Agent工具（可能与智能工具集重复）
ENABLE_SUB_AGENTS = False  # 禁用子Agent模式，只使用基础工具避免重复查询和协调问题


def setup_database(callback_context: CallbackContext) -> None:
    """Setup database connection and ensure sample data exists."""
    config = get_sqlite_config()

    # Setup DeepSeek API key for LiteLLM
    deepseek_api_key = get_deepseek_api_key()
    if deepseek_api_key:
        os.environ["DEEPSEEK_API_KEY"] = deepseek_api_key

    # 启用LLM监控，增加内容长度限制以显示完整结果，启用原始API监控，启用文件保存
    enable_monitoring(detailed_logging=True, max_content_length=2000, show_raw_api=True, save_to_file=True, output_dir="llm_logs")

    # Store config in session state
    callback_context.state["sqlite_config"] = config.model_dump()

    # 初始化会话状态用于智能工具选择
    callback_context.state["tables_explored"] = False
    callback_context.state["table_structure_known"] = False
    callback_context.state["query_history"] = []
    
    # Create sample database if it doesn't exist
    if not os.path.exists(config.database_path):
        print(f"Creating sample database at: {config.database_path}")
        create_sample_database(config.database_path)
    
    # Verify database connection
    try:
        with get_database_connection(config.database_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            table_names = [table[0] for table in tables] if tables else []
            callback_context.state["available_tables"] = table_names
            
            print(f"Connected to database: {config.database_path}")
            print(f"Available tables: {', '.join(table_names) if table_names else 'None'}")
            
    except Exception as e:
        print(f"Database connection error: {str(e)}")
        callback_context.state["database_error"] = str(e)


def get_database_context(callback_context: CallbackContext) -> str:
    """Get database context information for the agent."""
    config_dict = callback_context.state.get("sqlite_config", {})
    tables = callback_context.state.get("available_tables", [])
    
    context = f"""
## Current Database Information:
- **Database Path**: {config_dict.get('database_path', 'Unknown')}
- **Available Tables**: {', '.join(tables) if tables else 'None'}
- **Max Results**: {config_dict.get('max_results', 100)}
- **Read Only Mode**: {config_dict.get('read_only', True)}

## Available Tools:
1. **list_tables**: Get all table names in the database
2. **get_table_info**: Get detailed information about a specific table
3. **execute_query**: Execute SELECT queries safely
4. **get_sample_data**: Get sample data from tables

## Usage Tips:
- Start by listing tables or getting table info to understand the database structure
- Use sample data to see what kind of information is available
- Write SELECT queries to answer specific questions
- All queries are automatically validated for safety
"""
    
    return context


# 创建混合策略的SQLite Agent
sqlite_agent = Agent(
    name="sqlite_agent",
    model=create_monitored_llm(model=get_model_name()),
    instruction=lambda ctx: f"""
你是一个智能的SQLite数据库助手，能够根据用户需求智能选择最合适的工具和方法。

{get_sqlite_agent_instructions()}

{get_database_context(ctx)}

## 工作策略：
你需要直接使用基础工具来完成用户的数据库查询需求：

1. **理解用户需求**: 分析用户想要查询什么信息
2. **选择合适工具**: 根据需求选择最合适的基础工具
3. **执行查询**: 直接使用工具获取数据
4. **格式化结果**: 将结果以清晰易懂的方式呈现给用户

## 工具使用指南：
- **list_tables**: 当用户询问有哪些表时使用
- **get_table_info**: 当需要了解表结构时使用
- **get_sample_data**: 当需要查看数据示例时使用
- **execute_query**: 当需要执行具体SQL查询时使用

## 工作原则：
1. 直接使用基础工具，避免不必要的中间步骤
2. 确保查询安全性和结果准确性
3. 提供清晰的解释和格式化的结果
4. 建议相关的后续查询

记住：你的目标是高效、直接地帮助用户完成数据库查询。
""",
    tools=[
        # 智能工具集：根据用户意图动态筛选基础工具
        sqlite_smart_toolset,
    ] + (
        # 可选的子Agent工具：提供更专业的处理能力
        [
            AgentTool(agent=exploration_agent), # 探索Agent作为工具
            AgentTool(agent=query_agent),      # 查询Agent作为工具
            AgentTool(agent=analysis_agent),   # 分析Agent作为工具
        ] if ENABLE_SUB_AGENTS else []
    ),
    before_agent_callback=setup_database,
    generate_content_config=types.GenerateContentConfig(
        safety_settings=[
            types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            ),
        ],
        temperature=0.1,
        top_p=0.8,
        max_output_tokens=2048,
    ),
)
