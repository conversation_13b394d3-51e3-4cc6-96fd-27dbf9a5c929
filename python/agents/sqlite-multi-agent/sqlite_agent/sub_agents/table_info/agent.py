# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Table info agent - specialized agent for getting table structure information."""

from google.adk.agents import Agent
from google.genai import types

from ...config import get_model_name
from ...tools import get_table_info_tool
from ...monitored_llm import create_monitored_llm
from .prompt import TABLE_INFO_AGENT_INSTR


table_info_agent = Agent(
    name="table_info_agent",
    model=create_monitored_llm(model=get_model_name()),
    description="专门负责获取SQLite数据库表结构信息的agent，遵循单一职责原则",
    instruction=TABLE_INFO_AGENT_INSTR,
    tools=[get_table_info_tool],
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True,
    generate_content_config=types.GenerateContentConfig(
        temperature=0.1,
        top_p=0.8,
        max_output_tokens=1024,
    ),
)
