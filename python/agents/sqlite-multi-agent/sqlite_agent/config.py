# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Configuration management for SQLite Agent."""

import os
from typing import Optional
from pydantic import BaseModel, Field


class SQLiteConfig(BaseModel):
    """Configuration for SQLite database connection."""
    
    database_path: str = Field(
        default="data/sample.db",
        description="Path to the SQLite database file"
    )
    
    max_results: int = Field(
        default=100,
        description="Maximum number of results to return from queries"
    )
    
    timeout: int = Field(
        default=30,
        description="Query timeout in seconds"
    )
    
    read_only: bool = Field(
        default=True,
        description="Whether to open database in read-only mode"
    )


def get_sqlite_config() -> SQLiteConfig:
    """Get SQLite configuration from environment variables or defaults."""
    return SQLiteConfig(
        database_path=os.getenv("SQLITE_DATABASE_PATH", "data/sample.db"),
        max_results=int(os.getenv("SQLITE_MAX_RESULTS", "100")),
        timeout=int(os.getenv("SQLITE_TIMEOUT", "30")),
        read_only=os.getenv("SQLITE_READ_ONLY", "true").lower() == "true"
    )


def get_model_name() -> str:
    """Get the model name from environment variables."""
    return os.getenv("MODEL_NAME", "deepseek/deepseek-chat")


def get_deepseek_api_key() -> Optional[str]:
    """Get DeepSeek API key from environment variables."""
    return os.getenv("DEEPSEEK_API_KEY")
