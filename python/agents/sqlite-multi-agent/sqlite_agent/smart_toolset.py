# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Smart toolset for SQLite Agent with context-aware tool selection."""

import re
from typing import List, Dict, Any, Optional
from google.adk.tools.base_toolset import BaseToolset
from google.adk.tools.base_tool import BaseTool
from google.adk.agents.callback_context import CallbackContext

from .tools import (
    list_tables_tool,
    get_table_info_tool, 
    execute_query_tool,
    get_sample_data_tool
)


class SQLiteSmartToolset(BaseToolset):
    """Smart toolset that dynamically selects SQLite tools based on user intent."""
    
    def __init__(self, debug_enabled=False):
        # 基础工具列表 - 必须在super().__init__之前定义
        self.base_tools = [
            list_tables_tool,
            get_table_info_tool,
            execute_query_tool,
            get_sample_data_tool
        ]

        super().__init__(tool_filter=self._smart_filter)

        # 调试模式开关
        self._debug_enabled = debug_enabled

        # 定义工具分类（禁用子Agent模式后，确保基础工具覆盖所有需求）
        self.exploration_tools = ["list_tables", "get_table_info", "get_sample_data"]
        self.query_tools = ["execute_query", "get_table_info", "get_sample_data"]  # 增加get_sample_data支持查询验证
        self.analysis_tools = ["execute_query", "get_sample_data", "get_table_info", "list_tables"]  # 增加list_tables支持全面分析
        
        # 意图关键词映射
        self.intent_keywords = {
            "exploration": [
                "表", "table", "数据库", "database", "结构", "structure", 
                "有哪些", "显示", "列出", "list", "show", "什么表"
            ],
            "query": [
                "查询", "query", "select", "搜索", "search", "找", "find",
                "统计", "count", "计算", "where", "条件"
            ],
            "analysis": [
                "分析", "analysis", "统计", "statistics", "聚合", "aggregate",
                "平均", "average", "最大", "max", "最小", "min", "总和", "sum"
            ]
        }
    
    def _smart_filter(self, tool: BaseTool, readonly_context=None) -> bool:
        """智能工具过滤器，在禁用子Agent模式后简化逻辑，确保基础工具可用。"""

        # 禁用子Agent模式后，简化过滤逻辑
        # 确保所有基础工具都可用，避免工具缺失导致的错误

        if not tool or not hasattr(tool, 'name'):
            return False

        tool_name = tool.name

        # 基础工具总是可用
        basic_tools = ["list_tables", "get_table_info", "execute_query", "get_sample_data"]
        if tool_name in basic_tools:
            return True

        # 对于其他工具，如果没有上下文，也包含进来
        if not readonly_context:
            return True

        # 获取用户查询进行简单的意图分析
        user_query = self._get_user_query(readonly_context)
        if not user_query:
            return True

        # 简化的意图分析：只做基本的关键词匹配
        user_query = user_query.lower()

        # 探索类查询
        if any(keyword in user_query for keyword in ["表", "table", "数据库", "database", "显示", "列出", "list", "show"]):
            return tool_name in ["list_tables", "get_table_info", "get_sample_data"]

        # 查询类请求
        if any(keyword in user_query for keyword in ["查询", "query", "select", "搜索", "find", "最", "统计", "count"]):
            return tool_name in ["execute_query", "get_table_info", "get_sample_data"]

        # 默认包含所有基础工具
        return tool_name in basic_tools
    
    def _get_user_query(self, readonly_context) -> str:
        """从上下文中提取用户查询。"""
        try:
            # 从user_content中提取文本内容
            if hasattr(readonly_context, 'user_content') and readonly_context.user_content:
                content = readonly_context.user_content
                if content.parts:
                    # 提取所有文本部分
                    text_parts = []
                    for part in content.parts:
                        if hasattr(part, 'text') and part.text:
                            text_parts.append(part.text)
                    if text_parts:
                        return ' '.join(text_parts).lower()

            # 备用方案：从状态中获取
            if hasattr(readonly_context, 'state') and readonly_context.state:
                if 'current_query' in readonly_context.state:
                    return readonly_context.state['current_query'].lower()
                # 尝试从最近的消息历史中获取
                if 'query_history' in readonly_context.state and readonly_context.state['query_history']:
                    return readonly_context.state['query_history'][-1].lower()
        except Exception as e:
            # 可选的调试日志
            if hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] Error extracting user query: {e}")
                print(f"[DEBUG] Context attributes: {dir(readonly_context) if readonly_context else 'None'}")
                if readonly_context and hasattr(readonly_context, 'user_content'):
                    print(f"[DEBUG] User content: {readonly_context.user_content}")
        return ""
    
    def _analyze_intent(self, user_query: str) -> str:
        """分析用户查询意图。"""
        user_query = user_query.lower()

        # 计算每种意图的匹配分数
        intent_scores = {}

        for intent, keywords in self.intent_keywords.items():
            score = 0
            matched_keywords = []
            for keyword in keywords:
                if keyword in user_query:
                    score += 1
                    matched_keywords.append(keyword)
            intent_scores[intent] = score
            if matched_keywords and hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] Intent '{intent}' matched keywords: {matched_keywords} (score: {score})")

        if hasattr(self, '_debug_enabled') and self._debug_enabled:
            print(f"[DEBUG] Intent scores: {intent_scores}")

        # 返回得分最高的意图，如果没有匹配则返回exploration
        if not any(intent_scores.values()):
            if hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] No intent keywords matched, defaulting to 'exploration'")
            return "exploration"

        best_intent = max(intent_scores, key=intent_scores.get)
        if hasattr(self, '_debug_enabled') and self._debug_enabled:
            print(f"[DEBUG] Best intent: {best_intent}")
        return best_intent
    
    def _is_tool_relevant(self, tool_name: str, intent: str, user_query: str, readonly_context=None) -> bool:
        """判断工具是否与当前意图相关，包含上下文感知。"""

        # 基于意图的工具选择
        if intent == "exploration":
            relevant = tool_name in self.exploration_tools
            if hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] Tool {tool_name} in exploration_tools {self.exploration_tools}: {relevant}")
        elif intent == "query":
            relevant = tool_name in self.query_tools
            if hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] Tool {tool_name} in query_tools {self.query_tools}: {relevant}")
        elif intent == "analysis":
            relevant = tool_name in self.analysis_tools
            if hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] Tool {tool_name} in analysis_tools {self.analysis_tools}: {relevant}")
        else:
            relevant = True
            if hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] Unknown intent, defaulting to True for tool {tool_name}")

        # 上下文感知优化（只在基础筛选为False时才考虑，且仅在exploration意图时生效）
        if readonly_context and not relevant and intent == "exploration":
            try:
                session_state = getattr(readonly_context, 'state', {})
                if hasattr(self, '_debug_enabled') and self._debug_enabled:
                    print(f"[DEBUG] Session state: {session_state}")

                # 如果是新会话，优先显示探索工具
                if not session_state.get('tables_explored', False):
                    if tool_name in ["list_tables", "get_sample_data"]:
                        if hasattr(self, '_debug_enabled') and self._debug_enabled:
                            print(f"[DEBUG] New session, adding exploration tool: {tool_name}")
                        relevant = True

                # 如果已经探索过表结构，优先显示查询工具
                if session_state.get('table_structure_known', False):
                    if tool_name in ["execute_query", "get_table_info"]:
                        if hasattr(self, '_debug_enabled') and self._debug_enabled:
                            print(f"[DEBUG] Table structure known, adding query tool: {tool_name}")
                        relevant = True
            except Exception as e:
                if hasattr(self, '_debug_enabled') and self._debug_enabled:
                    print(f"[DEBUG] Context optimization error: {e}")
                pass

        # 特殊规则优化
        if not relevant:
            # 如果用户明确提到表名，总是包含get_table_info
            if re.search(r'\b\w+表\b|\btable\s+\w+\b', user_query):
                relevant = tool_name == "get_table_info"

            # 如果用户提到SQL或查询语句，总是包含execute_query
            if any(keyword in user_query for keyword in ["sql", "select", "where", "from"]):
                relevant = tool_name == "execute_query"

        return relevant
    
    async def get_tools(self, readonly_context=None) -> List[BaseTool]:
        """获取经过智能过滤的工具列表。"""

        if hasattr(self, '_debug_enabled') and self._debug_enabled:
            print(f"[DEBUG] Getting tools with context: {readonly_context is not None}")

        selected_tools = []

        for tool in self.base_tools:
            if self._is_tool_selected(tool, readonly_context):
                selected_tools.append(tool)

        if hasattr(self, '_debug_enabled') and self._debug_enabled:
            print(f"[DEBUG] Selected tools: {[tool.name for tool in selected_tools]}")

        # 确保至少有一个工具可用
        if not selected_tools:
            if hasattr(self, '_debug_enabled') and self._debug_enabled:
                print(f"[DEBUG] No tools selected, falling back to list_tables_tool")
            selected_tools = [list_tables_tool]

        if hasattr(self, '_debug_enabled') and self._debug_enabled:
            print(f"[DEBUG] Final tool list: {[tool.name for tool in selected_tools]}")
        return selected_tools

    async def close(self) -> None:
        """清理资源。"""
        # SQLite工具集不需要特殊的清理操作
        pass


# 创建工具集实例
sqlite_smart_toolset = SQLiteSmartToolset()
