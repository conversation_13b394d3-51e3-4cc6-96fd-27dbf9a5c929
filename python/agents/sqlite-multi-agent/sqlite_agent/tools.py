# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""SQLite tools for database operations."""

import sqlite3
import os
from typing import List, Dict, Any, Optional
from tabulate import tabulate

from google.adk.tools import FunctionTool
from google.adk.tools.tool_context import ToolContext

from .config import get_sqlite_config
from .utils import validate_query, format_results, get_database_connection


def get_database_schema(database_path: str) -> str:
    """Get the schema information for all tables in the database."""
    try:
        with get_database_connection(database_path) as conn:
            cursor = conn.cursor()
            
            # Get all table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if not tables:
                return "No tables found in the database."
            
            schema_info = []
            for (table_name,) in tables:
                # Get table schema
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                table_info = f"\n## Table: {table_name}\n"
                if columns:
                    headers = ["Column", "Type", "Not Null", "Default", "Primary Key"]
                    rows = []
                    for col in columns:
                        rows.append([
                            col[1],  # name
                            col[2],  # type
                            "YES" if col[3] else "NO",  # not null
                            col[4] if col[4] is not None else "",  # default
                            "YES" if col[5] else "NO"  # primary key
                        ])
                    table_info += tabulate(rows, headers=headers, tablefmt="grid")
                else:
                    table_info += "No column information available."
                
                schema_info.append(table_info)
            
            return "\n".join(schema_info)
            
    except Exception as e:
        return f"Error retrieving database schema: {str(e)}"


def list_tables() -> str:
    """List all tables in the SQLite database. Use when user asks about available tables, table names, or database structure overview."""
    config = get_sqlite_config()
    
    try:
        with get_database_connection(config.database_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if not tables:
                return "No tables found in the database."
            
            table_names = [table[0] for table in tables]
            return f"Tables in database: {', '.join(table_names)}"
            
    except Exception as e:
        return f"Error listing tables: {str(e)}"


def get_table_info(table_name: str) -> str:
    """Get detailed information about a specific table including columns, types, and constraints. Use when user asks about table structure or schema."""
    config = get_sqlite_config()
    
    try:
        with get_database_connection(config.database_path) as conn:
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?;", (table_name,))
            if not cursor.fetchone():
                return f"Table '{table_name}' does not exist."
            
            # Get column information
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            row_count = cursor.fetchone()[0]
            
            result = f"## Table: {table_name}\n"
            result += f"**Row count:** {row_count}\n\n"
            
            if columns:
                headers = ["Column", "Type", "Not Null", "Default", "Primary Key"]
                rows = []
                for col in columns:
                    rows.append([
                        col[1],  # name
                        col[2],  # type
                        "YES" if col[3] else "NO",  # not null
                        col[4] if col[4] is not None else "",  # default
                        "YES" if col[5] else "NO"  # primary key
                    ])
                result += tabulate(rows, headers=headers, tablefmt="grid")
            else:
                result += "No column information available."
            
            return result
            
    except Exception as e:
        return f"Error getting table info: {str(e)}"


def execute_query(query: str) -> str:
    """Execute a SELECT query on the SQLite database. Use for data retrieval, filtering, aggregation, and analysis. Only SELECT queries are allowed."""
    config = get_sqlite_config()
    
    # Validate query for safety
    validation_result = validate_query(query)
    if not validation_result["valid"]:
        return f"Query validation failed: {validation_result['error']}"
    
    try:
        with get_database_connection(config.database_path) as conn:
            cursor = conn.cursor()
            cursor.execute(query)
            
            # Get column names
            columns = [description[0] for description in cursor.description] if cursor.description else []
            
            # Fetch results with limit
            results = cursor.fetchmany(config.max_results)
            
            if not results:
                return "Query executed successfully but returned no results."
            
            # Format results
            return format_results(results, columns, config.max_results)
            
    except Exception as e:
        return f"Error executing query: {str(e)}"


def get_sample_data(table_name: str, limit: int = 5) -> str:
    """Get sample data from a specific table to understand data format and content. Use when user wants to see example records or data preview."""
    config = get_sqlite_config()
    
    try:
        with get_database_connection(config.database_path) as conn:
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?;", (table_name,))
            if not cursor.fetchone():
                return f"Table '{table_name}' does not exist."
            
            # Get sample data
            cursor.execute(f"SELECT * FROM {table_name} LIMIT ?;", (limit,))
            results = cursor.fetchall()
            
            if not results:
                return f"Table '{table_name}' is empty."
            
            # Get column names
            columns = [description[0] for description in cursor.description]
            
            # Format results
            formatted_results = tabulate(results, headers=columns, tablefmt="grid")
            return f"Sample data from table '{table_name}' (showing up to {limit} rows):\n\n{formatted_results}"
            
    except Exception as e:
        return f"Error getting sample data: {str(e)}"


# Create ADK function tools
list_tables_tool = FunctionTool(list_tables)
get_table_info_tool = FunctionTool(get_table_info)
execute_query_tool = FunctionTool(execute_query)
get_sample_data_tool = FunctionTool(get_sample_data)

# Export all tools
sqlite_tools = [
    list_tables_tool,
    get_table_info_tool,
    execute_query_tool,
    get_sample_data_tool
]
