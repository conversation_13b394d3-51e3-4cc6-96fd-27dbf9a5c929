# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""带监控功能的LLM包装器"""

import time
import json
from typing import AsyncGenerator, Dict, Any, Optional, List
from google.adk.models.lite_llm import LiteLlm
from google.adk.models.llm_request import LlmRequest
from google.adk.models.llm_response import LlmResponse
from google.genai import types

from .llm_monitor import get_monitor

# 全局监控器实例
_monitor_instance = None

def get_global_monitor():
    global _monitor_instance
    if _monitor_instance is None:
        _monitor_instance = get_monitor()
    return _monitor_instance

# 猴子补丁方式添加监控功能
original_generate_content_async = LiteLlm.generate_content_async

async def monitored_generate_content_async(
    self, llm_request: LlmRequest, stream: bool = False
) -> AsyncGenerator[LlmResponse, None]:
    """带监控功能的生成内容方法"""

    start_time = time.time()

    # 构建输入内容用于监控
    input_content = build_input_content_for_monitoring(llm_request)

    # 构建原始请求数据用于监控
    raw_request = build_raw_request_for_monitoring(llm_request)

    # 收集输出内容和原始响应
    output_parts = []
    function_calls = []
    raw_responses = []
    usage_metadata = None  # 收集真实的usage信息

    # 调用原始方法
    async for response in original_generate_content_async(self, llm_request, stream):
        # 收集原始响应数据
        if response:
            raw_responses.append(build_raw_response_for_monitoring(response))
            # 收集usage_metadata
            if response.usage_metadata:
                usage_metadata = response.usage_metadata

        if response.content and response.content.parts:
            for part in response.content.parts:
                if part.function_call:
                    function_calls.append(part.function_call)
                elif part.text:
                    output_parts.append(part.text)

        yield response

    # 计算耗时
    duration_ms = (time.time() - start_time) * 1000

    # 合并原始响应
    merged_raw_response = merge_raw_responses(raw_responses) if raw_responses else None

    # 记录交互
    monitor = get_global_monitor()
    if monitor:
        if function_calls:
            # 函数调用交互
            for func_call in function_calls:
                output_content = f"Function Call: {func_call.name}({func_call.args})"
                monitor.log_interaction(
                    interaction_type="function_call",
                    input_content=input_content,
                    output_content=output_content,
                    function_name=func_call.name,
                    function_args=func_call.args,
                    duration_ms=duration_ms,
                    raw_request=raw_request,
                    raw_response=merged_raw_response,
                    usage_metadata=usage_metadata  # 传递真实的usage信息
                )
        elif output_parts:
            # 文本回答交互
            output_content = "".join(output_parts)
            interaction_type = "final_answer" if len(output_content) > 50 else "tool_selection"
            monitor.log_interaction(
                interaction_type=interaction_type,
                input_content=input_content,
                output_content=output_content,
                duration_ms=duration_ms,
                raw_request=raw_request,
                raw_response=merged_raw_response,
                usage_metadata=usage_metadata  # 传递真实的usage信息
            )
    
def build_input_content_for_monitoring(llm_request: LlmRequest) -> str:
    """构建用于监控的输入内容"""

    input_parts = []

    # System instruction
    if llm_request.config and llm_request.config.system_instruction:
        input_parts.append(f"[SYSTEM] {llm_request.config.system_instruction}")

    # Messages
    if llm_request.contents:
        for content in llm_request.contents:
            role = content.role
            if content.parts:
                for part in content.parts:
                    if part.text:
                        input_parts.append(f"[{role.upper()}] {part.text}")
                    elif part.function_call:
                        input_parts.append(f"[{role.upper()}] Function Call: {part.function_call.name}")
                    elif part.function_response:
                        response_preview = str(part.function_response.response)[:200]
                        input_parts.append(f"[TOOL] {part.function_response.name}: {response_preview}...")

    # Tools (只在第一次调用时显示)
    if (llm_request.config and
        llm_request.config.tools and
        len(llm_request.contents) <= 1):  # 只在初始请求时显示工具

        tool_names = []
        # 收集所有工具声明中的工具名称
        for tool in llm_request.config.tools:
            if tool.function_declarations:
                for func_decl in tool.function_declarations:
                    tool_names.append(func_decl.name)

        if tool_names:
            # 显示实际被筛选后的工具列表
            input_parts.append(f"[TOOLS] Available (after filtering): {', '.join(tool_names)}")
            input_parts.append(f"[TOOLS] Total count: {len(tool_names)}")

    return "\n".join(input_parts)


def build_raw_request_for_monitoring(llm_request: LlmRequest) -> Dict[str, Any]:
    """构建用于监控的原始请求数据"""

    # 构建消息列表
    messages = []

    # 添加系统指令
    if llm_request.config and llm_request.config.system_instruction:
        messages.append({
            "role": "system",
            "content": llm_request.config.system_instruction
        })

    # 添加对话消息
    if llm_request.contents:
        for content in llm_request.contents:
            message = {"role": content.role}

            if content.parts:
                # 处理文本内容
                text_parts = [part.text for part in content.parts if part.text]
                if text_parts:
                    message["content"] = "".join(text_parts)

                # 处理函数调用
                tool_calls = []
                for part in content.parts:
                    if part.function_call:
                        tool_calls.append({
                            "type": "function",
                            "function": {
                                "name": part.function_call.name,
                                "arguments": json.dumps(part.function_call.args)
                            }
                        })

                if tool_calls:
                    message["tool_calls"] = tool_calls

                # 处理函数响应
                if any(part.function_response for part in content.parts):
                    for part in content.parts:
                        if part.function_response:
                            messages.append({
                                "role": "tool",
                                "name": part.function_response.name,
                                "content": str(part.function_response.response)
                            })
                            continue

            if "content" in message or "tool_calls" in message:
                messages.append(message)

    # 构建工具声明 - 显示实际被筛选后的工具
    tools = []
    if llm_request.config and llm_request.config.tools:
        for tool in llm_request.config.tools:
            if tool.function_declarations:
                for func_decl in tool.function_declarations:
                    # 转换parameters为可序列化的字典
                    parameters = {}
                    if func_decl.parameters:
                        try:
                            # 尝试转换为字典
                            if hasattr(func_decl.parameters, 'model_dump'):
                                parameters = func_decl.parameters.model_dump()
                            elif hasattr(func_decl.parameters, 'dict'):
                                parameters = func_decl.parameters.dict()
                            else:
                                parameters = dict(func_decl.parameters)
                        except:
                            parameters = {"type": "object", "properties": {}}

                    tools.append({
                        "type": "function",
                        "function": {
                            "name": func_decl.name,
                            "description": func_decl.description or f"Tool: {func_decl.name} (filtered)",
                            "parameters": parameters
                        }
                    })

    # 构建完整请求
    request_data = {
        "model": "deepseek/deepseek-chat",
        "messages": messages
    }

    if tools:
        request_data["tools"] = tools

    # 添加生成配置
    if llm_request.config:
        request_data["temperature"] = 0.1
        request_data["top_p"] = 0.8
        request_data["max_tokens"] = 2048

    return request_data


def build_raw_response_for_monitoring(llm_response: LlmResponse) -> Dict[str, Any]:
    """构建用于监控的原始响应数据"""

    response_data = {
        "id": "chatcmpl-monitoring",
        "object": "chat.completion",
        "model": "deepseek-chat",
        "choices": []
    }

    if llm_response.content and llm_response.content.parts:
        choice = {
            "index": 0,
            "message": {
                "role": "assistant"
            },
            "finish_reason": "stop"
        }

        # 处理文本内容
        text_parts = [part.text for part in llm_response.content.parts if part.text]
        if text_parts:
            choice["message"]["content"] = "".join(text_parts)

        # 处理函数调用
        tool_calls = []
        for part in llm_response.content.parts:
            if part.function_call:
                tool_calls.append({
                    "type": "function",
                    "function": {
                        "name": part.function_call.name,
                        "arguments": json.dumps(part.function_call.args)
                    }
                })

        if tool_calls:
            choice["message"]["tool_calls"] = tool_calls
            choice["finish_reason"] = "tool_calls"

        response_data["choices"].append(choice)

    return response_data


def merge_raw_responses(raw_responses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """合并多个原始响应（用于流式响应）"""
    if not raw_responses:
        return {}

    # 使用第一个响应作为基础
    merged = raw_responses[0].copy()

    # 如果有多个响应，合并内容
    if len(raw_responses) > 1:
        all_content = []
        for response in raw_responses:
            if response.get("choices") and response["choices"][0].get("message", {}).get("content"):
                all_content.append(response["choices"][0]["message"]["content"])

        if all_content and merged.get("choices"):
            merged["choices"][0]["message"]["content"] = "".join(all_content)

    return merged


# 应用猴子补丁
LiteLlm.generate_content_async = monitored_generate_content_async

def create_monitored_llm(model: str, **kwargs) -> LiteLlm:
    """创建带监控功能的LLM实例"""
    return LiteLlm(model=model, **kwargs)
