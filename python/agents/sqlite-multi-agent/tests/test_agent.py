# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Tests for SQLite Agent."""

import os
import tempfile
import pytest
from unittest.mock import patch

from sqlite_agent.agent import sqlite_agent
from sqlite_agent.utils import create_sample_database, validate_query
from sqlite_agent.config import SQLiteConfig


class TestSQLiteAgent:
    """Test cases for SQLite Agent."""
    
    def setup_method(self):
        """Setup test database."""
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        create_sample_database(self.temp_db.name)
    
    def teardown_method(self):
        """Cleanup test database."""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    @patch.dict(os.environ, {'SQLITE_DATABASE_PATH': ''})
    def test_agent_creation(self):
        """Test that agent can be created successfully."""
        assert sqlite_agent is not None
        assert sqlite_agent.name == "sqlite_agent"
        assert len(sqlite_agent.tools) == 4
    
    def test_query_validation(self):
        """Test query validation function."""
        # Valid queries
        assert validate_query("SELECT * FROM employees")["valid"]
        assert validate_query("SELECT name, salary FROM employees WHERE salary > 70000")["valid"]
        
        # Invalid queries
        assert not validate_query("INSERT INTO employees VALUES (1, 'Test')")["valid"]
        assert not validate_query("UPDATE employees SET salary = 80000")["valid"]
        assert not validate_query("DELETE FROM employees")["valid"]
        assert not validate_query("DROP TABLE employees")["valid"]
        assert not validate_query("SELECT * FROM employees; DROP TABLE employees;")["valid"]
        assert not validate_query("SELECT * FROM employees -- comment")["valid"]
    
    def test_sample_database_creation(self):
        """Test sample database creation."""
        # Database should exist and have tables
        assert os.path.exists(self.temp_db.name)
        
        # Test database connection and tables
        import sqlite3
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            
            assert 'employees' in table_names
            assert 'departments' in table_names
            
            # Test data exists
            cursor.execute("SELECT COUNT(*) FROM employees;")
            employee_count = cursor.fetchone()[0]
            assert employee_count > 0


class TestSQLiteConfig:
    """Test cases for SQLite configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = SQLiteConfig()
        assert config.database_path == "data/sample.db"
        assert config.max_results == 100
        assert config.timeout == 30
        assert config.read_only is True
    
    @patch.dict(os.environ, {
        'SQLITE_DATABASE_PATH': '/custom/path.db',
        'SQLITE_MAX_RESULTS': '50',
        'SQLITE_TIMEOUT': '60',
        'SQLITE_READ_ONLY': 'false',
        'DEEPSEEK_API_KEY': 'test-key'
    })
    def test_config_from_env(self):
        """Test configuration from environment variables."""
        from sqlite_agent.config import get_sqlite_config
        config = get_sqlite_config()
        
        assert config.database_path == '/custom/path.db'
        assert config.max_results == 50
        assert config.timeout == 60
        assert config.read_only is False


if __name__ == "__main__":
    pytest.main([__file__])
