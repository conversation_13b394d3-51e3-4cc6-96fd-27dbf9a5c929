#!/usr/bin/env python3
"""
SQLite多agent系统交互式演示
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlite_agent.agent import sqlite_agent
from google.adk.runners import InMemoryRunner


async def interactive_demo():
    """交互式演示"""
    print("🎯 SQLite多agent系统交互式演示")
    print("=" * 50)
    
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ 请先设置DEEPSEEK_API_KEY环境变量")
        print("   export DEEPSEEK_API_KEY='your-api-key'")
        return
    
    # 设置默认环境变量
    os.environ.setdefault("MODEL_NAME", "deepseek/deepseek-chat")
    os.environ.setdefault("SQLITE_DATABASE_PATH", "data/sample.db")
    
    try:
        # 创建runner
        runner = InMemoryRunner(
            app_name="sqlite_multi_agent_interactive",
            agent=sqlite_agent
        )
        
        print("✅ 系统初始化成功！")
        print(f"📊 包含 {len(sqlite_agent.sub_agents)} 个专门的子agent")
        print()
        
        # 预定义的示例查询
        sample_queries = [
            "有哪些表？",
            "显示employees表的结构",
            "显示employees表的样本数据",
            "查询员工总数",
            "显示所有部门信息"
        ]
        
        print("🔍 示例查询:")
        for i, query in enumerate(sample_queries, 1):
            print(f"   {i}. {query}")
        
        print("\n" + "=" * 50)
        print("💡 输入查询 (输入 'quit' 退出):")
        
        while True:
            try:
                user_input = input("\n🤔 你的问题: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                print(f"\n🤖 处理中...")
                
                # 这里应该调用runner.run()，但需要实际的API key
                print(f"📝 查询: {user_input}")
                print("🎯 系统会自动选择合适的子agent处理这个查询")
                
                # 根据查询类型提示会使用哪个agent
                if "表" in user_input and ("有哪些" in user_input or "列出" in user_input):
                    print("🔄 → 将使用 list_tables_agent")
                elif "结构" in user_input or "字段" in user_input:
                    print("🔄 → 将使用 table_info_agent")
                elif "样本" in user_input or "示例" in user_input:
                    print("🔄 → 将使用 sample_data_agent")
                elif "查询" in user_input or "SELECT" in user_input.upper():
                    print("🔄 → 将使用 query_execution_agent")
                else:
                    print("🔄 → 主agent将分析并选择最合适的子agent")
                
                # 实际使用时取消注释这行
                # response = await runner.run(user_input)
                # print(f"🤖 回答: {response}")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 处理错误: {e}")
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")


if __name__ == "__main__":
    asyncio.run(interactive_demo())
