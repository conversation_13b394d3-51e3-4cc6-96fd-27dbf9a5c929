# SQLite Agent Token使用量追踪改进

## 问题描述

之前的sqlite-agent项目中，LLM交互日志中的`total_tokens`、`input_tokens`和`output_tokens`都是基于本地tiktoken估算的，而不是LLM API真实返回的token使用量。这导致：

1. **不准确的token计算**：使用OpenAI的tiktoken估算DeepSeek模型的token使用量
2. **成本计算误差**：基于不准确的token数据进行成本估算
3. **无法区分数据来源**：用户无法知道token数据是真实的还是估算的

## 解决方案

### 1. 修改LLMMonitor类

**文件**: `sqlite_agent/llm_monitor.py`

**主要改动**:
- `log_interaction()`方法新增`usage_metadata`参数
- 优先使用真实的API token数据，备用本地估算
- 添加`token_source`字段标识数据来源
- 在日志显示中添加token来源标识

```python
def log_interaction(
    self,
    # ... 其他参数
    usage_metadata: Optional[Any] = None  # 新增参数
) -> LLMInteraction:
    # 优先使用真实的token使用量
    if usage_metadata:
        input_tokens = getattr(usage_metadata, 'prompt_token_count', 0)
        output_tokens = getattr(usage_metadata, 'candidates_token_count', 0)
        total_tokens = getattr(usage_metadata, 'total_token_count', input_tokens + output_tokens)
    elif raw_response and 'usage' in raw_response:
        # 从raw_response中提取usage信息（备用方案）
        usage = raw_response['usage']
        input_tokens = usage.get('prompt_tokens', 0)
        output_tokens = usage.get('completion_tokens', 0)
        total_tokens = usage.get('total_tokens', input_tokens + output_tokens)
    else:
        # 使用本地估算（最后的备用方案）
        input_tokens = self.count_tokens(input_content)
        output_tokens = self.count_tokens(output_content)
        total_tokens = input_tokens + output_tokens
```

### 2. 修改monitored_llm.py

**文件**: `sqlite_agent/monitored_llm.py`

**主要改动**:
- 从`LlmResponse.usage_metadata`中提取真实的token使用量
- 将`usage_metadata`传递给监控器

```python
# 收集usage_metadata
usage_metadata = None
async for response in original_generate_content_async(self, llm_request, stream):
    if response and response.usage_metadata:
        usage_metadata = response.usage_metadata
    # ...

# 传递给监控器
monitor.log_interaction(
    # ... 其他参数
    usage_metadata=usage_metadata
)
```

### 3. 数据结构更新

**LLMInteraction数据类**新增字段：
```python
@dataclass
class LLMInteraction:
    # ... 其他字段
    token_source: Optional[str] = None  # "api" 或 "estimated"
```

## 改进效果

### 1. Token数据来源优先级

1. **最高优先级**: `LlmResponse.usage_metadata` (真实API返回)
2. **中等优先级**: `raw_response['usage']` (备用API数据)
3. **最低优先级**: 本地tiktoken估算 (兜底方案)

### 2. 日志显示改进

**修改前**:
```
📊 Token消耗: 输入784 + 输出13 = 总计797
```

**修改后**:
```
📊 Token消耗: 输入784 + 输出13 = 总计797 ✅ (真实API返回)
📊 Token消耗: 输入6 + 输出6 = 总计12 ⚠️ (本地估算)
```

### 3. 测试结果对比

| 数据来源 | 输入tokens | 输出tokens | 总计tokens | 准确性 |
|---------|-----------|-----------|-----------|--------|
| 真实API | 100 | 20 | 120 | ✅ 100% |
| 本地估算 | 6 | 6 | 12 | ⚠️ 90%差异 |

## 使用方法

### 1. 运行sqlite-agent

```bash
cd python/agents/sqlite-agent
python3 -m sqlite_agent.main
```

### 2. 查看日志

在LLM交互日志中查找token来源标识：
- `✅ (真实API返回)`: 使用DeepSeek API返回的真实token数据
- `⚠️ (本地估算)`: 使用本地tiktoken估算（当API未返回usage信息时）

### 3. 验证改进

运行测试脚本验证功能：
```bash
python3 test_token_tracking.py
```

## 技术细节

### 1. ADK框架集成

利用ADK框架的`LlmResponse.usage_metadata`属性：
```python
# ADK LlmResponse结构
class LlmResponse:
    usage_metadata: Optional[types.GenerateContentResponseUsageMetadata] = None
    
# GenerateContentResponseUsageMetadata结构
class GenerateContentResponseUsageMetadata:
    prompt_token_count: int
    candidates_token_count: int  
    total_token_count: int
```

### 2. LiteLLM集成

LiteLLM会将DeepSeek API的usage信息转换为ADK格式：
```python
# LiteLLM转换逻辑
if response.get("usage", None):
    llm_response.usage_metadata = types.GenerateContentResponseUsageMetadata(
        prompt_token_count=response["usage"].get("prompt_tokens", 0),
        candidates_token_count=response["usage"].get("completion_tokens", 0),
        total_token_count=response["usage"].get("total_tokens", 0),
    )
```

### 3. 向后兼容性

- 保持原有API接口不变
- 新增的`usage_metadata`参数为可选
- 当没有真实token数据时自动降级到本地估算
- 现有代码无需修改即可获得改进

## 预期收益

1. **准确的成本计算**: 基于真实token使用量进行成本估算
2. **性能监控**: 准确追踪不同查询类型的token消耗
3. **调试支持**: 清楚区分真实数据和估算数据
4. **优化指导**: 基于准确数据优化prompt和工具选择策略

## 总结

通过这次改进，sqlite-agent现在能够：
- ✅ 使用DeepSeek API返回的真实token使用量
- ✅ 在日志中清楚标识数据来源
- ✅ 提供更准确的token统计和成本计算
- ✅ 保持向后兼容性和稳定性

这为后续的性能优化和成本控制提供了可靠的数据基础。
