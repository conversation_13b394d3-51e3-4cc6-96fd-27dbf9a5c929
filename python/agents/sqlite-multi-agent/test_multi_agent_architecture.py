#!/usr/bin/env python3
"""
测试新的多agent架构
验证每个子agent都能独立完成其职责
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlite_agent.agent import sqlite_agent
from sqlite_agent.sub_agents import (
    list_tables_agent,
    table_info_agent,
    query_execution_agent,
    sample_data_agent
)
from google.adk.runners import InMemoryRunner


async def test_individual_agents():
    """测试每个子agent的独立功能"""
    print("🧪 测试各个子agent的独立功能...")
    
    # 设置环境变量
    os.environ["DEEPSEEK_API_KEY"] = os.getenv("DEEPSEEK_API_KEY", "test-key")
    os.environ["MODEL_NAME"] = "deepseek/deepseek-chat"
    os.environ["SQLITE_DATABASE_PATH"] = "data/sample.db"
    
    try:
        # 测试 list_tables_agent
        print("\n📋 测试 list_tables_agent...")
        list_runner = InMemoryRunner(
            app_name="test_list_tables",
            agent=list_tables_agent
        )
        
        # 测试 table_info_agent
        print("\n📊 测试 table_info_agent...")
        info_runner = InMemoryRunner(
            app_name="test_table_info", 
            agent=table_info_agent
        )
        
        # 测试 sample_data_agent
        print("\n📄 测试 sample_data_agent...")
        sample_runner = InMemoryRunner(
            app_name="test_sample_data",
            agent=sample_data_agent
        )
        
        # 测试 query_execution_agent
        print("\n🔍 测试 query_execution_agent...")
        query_runner = InMemoryRunner(
            app_name="test_query_execution",
            agent=query_execution_agent
        )
        
        print("✅ 所有子agent初始化成功！")
        
    except Exception as e:
        print(f"❌ 子agent测试失败: {e}")
        return False
    
    return True


async def test_main_agent():
    """测试主agent的多agent协调功能"""
    print("\n🎯 测试主agent的多agent协调功能...")
    
    try:
        # 创建主agent runner
        runner = InMemoryRunner(
            app_name="test_sqlite_multi_agent",
            agent=sqlite_agent
        )
        
        print("✅ 主agent初始化成功！")
        print("📝 主agent包含以下子agents:")
        print("   - list_tables_agent: 列出数据库表")
        print("   - table_info_agent: 获取表结构信息")
        print("   - query_execution_agent: 执行SQL查询")
        print("   - sample_data_agent: 获取样本数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 主agent测试失败: {e}")
        return False


async def test_architecture_integrity():
    """测试架构完整性"""
    print("\n🏗️ 测试多agent架构完整性...")
    
    try:
        # 检查导入
        from sqlite_agent.sub_agents import (
            list_tables_agent,
            table_info_agent,
            query_execution_agent,
            sample_data_agent
        )
        
        # 检查agent属性
        agents = [list_tables_agent, table_info_agent, query_execution_agent, sample_data_agent]
        
        for agent in agents:
            assert hasattr(agent, 'name'), f"Agent {agent} 缺少 name 属性"
            assert hasattr(agent, 'tools'), f"Agent {agent} 缺少 tools 属性"
            assert hasattr(agent, 'instruction'), f"Agent {agent} 缺少 instruction 属性"
            print(f"   ✅ {agent.name} - 结构完整")
        
        # 检查主agent
        assert hasattr(sqlite_agent, 'sub_agents'), "主agent缺少sub_agents属性"
        assert len(sqlite_agent.sub_agents) == 4, f"主agent应该有4个子agent，实际有{len(sqlite_agent.sub_agents)}个"
        
        print("✅ 架构完整性检查通过！")
        return True
        
    except Exception as e:
        print(f"❌ 架构完整性检查失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试SQLite多agent架构...")
    print("=" * 60)
    
    # 测试各个组件
    tests = [
        ("架构完整性", test_architecture_integrity),
        ("子agent功能", test_individual_agents),
        ("主agent协调", test_main_agent),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔬 执行测试: {test_name}")
        print("-" * 40)
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！多agent架构重构成功！")
        print("\n📋 架构特点:")
        print("   • 每个工具都有专门的agent负责")
        print("   • 遵循单一职责原则")
        print("   • 主agent负责协调调用")
        print("   • 参考travel-concierge的多agent模式")
    else:
        print("⚠️  部分测试失败，需要进一步调试")
    
    return passed == len(results)


if __name__ == "__main__":
    asyncio.run(main())
