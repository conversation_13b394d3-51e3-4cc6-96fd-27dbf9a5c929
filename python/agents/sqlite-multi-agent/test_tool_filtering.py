#!/usr/bin/env python3
"""
测试工具筛选机制的脚本
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlite_agent.smart_toolset import SQLiteSmartToolset
from sqlite_agent.tools import list_tables_tool, get_table_info_tool, execute_query_tool, get_sample_data_tool


class MockReadonlyContext:
    """模拟ReadonlyContext用于测试"""
    
    def __init__(self, user_query: str):
        self.user_content = MockContent(user_query)
        self.state = {}


class MockContent:
    """模拟Content对象"""
    
    def __init__(self, text: str):
        self.parts = [MockPart(text)]


class MockPart:
    """模拟Part对象"""
    
    def __init__(self, text: str):
        self.text = text


async def test_tool_filtering():
    """测试工具筛选功能"""
    
    print("=" * 60)
    print("测试工具筛选机制")
    print("=" * 60)
    
    # 创建智能工具集
    smart_toolset = SQLiteSmartToolset()
    
    # 测试不同类型的查询
    test_cases = [
        ("显示所有表", "exploration"),
        ("users表的结构", "exploration"), 
        ("查询用户数据", "query"),
        ("SELECT * FROM orders", "query"),
        ("分析销售趋势", "analysis"),
        ("统计订单信息", "analysis"),
        ("计算平均价格", "analysis"),
        ("最贵的订单", "analysis"),
    ]
    
    for query, expected_intent in test_cases:
        print(f"\n🔍 测试查询: '{query}'")
        print(f"📋 预期意图: {expected_intent}")
        
        # 创建模拟上下文
        context = MockReadonlyContext(query)
        
        # 获取筛选后的工具
        try:
            tools = await smart_toolset.get_tools(context)
            tool_names = [tool.name for tool in tools]
            
            print(f"🛠️  筛选后的工具: {tool_names}")
            print(f"📊 工具数量: {len(tools)}")
            
            # 验证工具筛选是否合理
            if expected_intent == "exploration":
                expected_tools = ["list_tables", "get_table_info", "get_sample_data"]
            elif expected_intent == "query":
                expected_tools = ["execute_query", "get_table_info"]
            elif expected_intent == "analysis":
                expected_tools = ["execute_query", "get_sample_data", "get_table_info"]
            
            # 检查是否包含预期的工具
            has_expected = any(tool in tool_names for tool in expected_tools)
            print(f"✅ 包含预期工具: {has_expected}")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 40)
    
    # 测试无上下文情况
    print(f"\n🔍 测试无上下文情况")
    try:
        tools = await smart_toolset.get_tools(None)
        tool_names = [tool.name for tool in tools]
        print(f"🛠️  工具: {tool_names}")
        print(f"📊 工具数量: {len(tools)}")
    except Exception as e:
        print(f"❌ 错误: {e}")


async def test_intent_analysis():
    """测试意图分析功能"""
    
    print("\n" + "=" * 60)
    print("测试意图分析功能")
    print("=" * 60)
    
    smart_toolset = SQLiteSmartToolset()
    
    test_queries = [
        "显示所有表",
        "列出数据库中的表",
        "查询用户信息",
        "SELECT * FROM users WHERE age > 25",
        "分析销售数据",
        "统计订单总数",
        "计算平均价格",
        "找到最贵的产品",
        "什么是数据库结构",
        "随机查询文本",
    ]
    
    for query in test_queries:
        print(f"\n📝 查询: '{query}'")
        intent = smart_toolset._analyze_intent(query)
        print(f"🎯 识别意图: {intent}")


if __name__ == "__main__":
    print("开始测试工具筛选机制...")
    
    # 运行测试
    asyncio.run(test_tool_filtering())
    asyncio.run(test_intent_analysis())
    
    print("\n✅ 测试完成!")
