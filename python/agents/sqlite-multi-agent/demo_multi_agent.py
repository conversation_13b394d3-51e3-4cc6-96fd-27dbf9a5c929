#!/usr/bin/env python3
"""
SQLite多agent架构演示脚本

展示新的多agent架构如何工作：
- 每个工具都有专门的agent负责
- 主agent负责协调调用子agent
- 遵循单一职责原则
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlite_agent.agent import sqlite_agent
from google.adk.runners import InMemoryRunner


async def demo_multi_agent_architecture():
    """演示多agent架构的使用"""
    print("🎯 SQLite多agent架构演示")
    print("=" * 60)
    
    # 设置环境变量
    os.environ["DEEPSEEK_API_KEY"] = os.getenv("DEEPSEEK_API_KEY", "test-key")
    os.environ["MODEL_NAME"] = "deepseek/deepseek-chat"
    os.environ["SQLITE_DATABASE_PATH"] = "data/sample.db"
    
    print("📋 新架构特点:")
    print("   • list_tables_agent: 专门负责列出数据库表")
    print("   • table_info_agent: 专门负责获取表结构信息")
    print("   • query_execution_agent: 专门负责执行SQL查询")
    print("   • sample_data_agent: 专门负责获取样本数据")
    print("   • 主agent: 负责协调调用各个子agent")
    print()
    
    try:
        # 创建主agent runner
        runner = InMemoryRunner(
            app_name="sqlite_multi_agent_demo",
            agent=sqlite_agent
        )
        
        print("✅ SQLite多agent系统初始化成功！")
        print()
        print("🚀 系统已准备就绪，可以处理以下类型的请求:")
        print("   1. 列出数据库表: '有哪些表？'")
        print("   2. 查看表结构: '显示users表的结构'")
        print("   3. 获取样本数据: '显示employees表的样本数据'")
        print("   4. 执行查询: '查询所有员工的姓名和部门'")
        print()
        print("💡 每个请求都会被路由到最合适的专门agent处理")
        
        # 演示架构信息
        print("\n🏗️ 架构信息:")
        print(f"   主agent名称: {sqlite_agent.name}")
        print(f"   子agent数量: {len(sqlite_agent.sub_agents)}")
        print("   子agent列表:")
        for i, sub_agent in enumerate(sqlite_agent.sub_agents, 1):
            print(f"     {i}. {sub_agent.name} - {sub_agent.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False


async def show_architecture_comparison():
    """展示新旧架构的对比"""
    print("\n📊 架构对比:")
    print("=" * 60)
    
    print("🔴 旧架构 (工具模式):")
    print("   • 主agent直接调用工具")
    print("   • 工具之间缺乏专门化")
    print("   • 难以实现复杂的协调逻辑")
    print("   • 不符合单一职责原则")
    print()
    
    print("🟢 新架构 (多agent模式):")
    print("   • 每个工具都有专门的agent")
    print("   • 每个agent遵循单一职责原则")
    print("   • 主agent负责协调调用")
    print("   • 参考travel-concierge的成熟模式")
    print("   • 更好的可扩展性和维护性")
    print()
    
    print("🎯 改进效果:")
    print("   ✅ 更清晰的职责分工")
    print("   ✅ 更好的代码组织")
    print("   ✅ 更容易扩展新功能")
    print("   ✅ 更符合ADK的最佳实践")


async def main():
    """主函数"""
    print("🌟 欢迎使用SQLite多agent架构演示")
    print()
    
    # 运行演示
    success = await demo_multi_agent_architecture()
    
    if success:
        await show_architecture_comparison()
        
        print("\n" + "=" * 60)
        print("🎉 多agent架构重构完成！")
        print()
        print("📝 重构总结:")
        print("   • 成功将4个工具转换为4个专门的agent")
        print("   • 实现了真正的多agent协调模式")
        print("   • 遵循了单一职责设计原则")
        print("   • 参考了travel-concierge的架构模式")
        print()
        print("🚀 现在可以使用新的多agent系统进行数据库查询了！")
    else:
        print("❌ 演示失败，请检查配置和依赖")


if __name__ == "__main__":
    asyncio.run(main())
